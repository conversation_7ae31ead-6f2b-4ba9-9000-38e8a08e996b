import React, { useState } from 'react';
import { ChevronDownIcon, SearchIcon } from './icons/Icons';
import './MobileSearchResults.css';

interface SearchResultItem {
  id: string;
  title: string;
  content?: string;
  details?: string[];
  sources?: Array<{
    title: string;
    url: string;
    domain: string;
  }>;
}

interface MobileSearchResultsProps {
  title?: string;
  totalSources?: number;
  results: SearchResultItem[];
  isExpanded?: boolean;
  onToggle?: () => void;
}

const MobileSearchResults: React.FC<MobileSearchResultsProps> = ({
  title = "DeepSearch",
  totalSources = 34,
  results = [],
  isExpanded = false,
  onToggle
}) => {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  const toggleItem = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  return (
    <div className="mobile-search-container">
      {/* 头部 - 可点击折叠 */}
      <div
        className="mobile-search-header"
        onClick={onToggle}
      >
        <div className="mobile-search-header-content">
          <SearchIcon
            width={20}
            height={20}
            stroke="#2563eb"
          />
          <div>
            <span className="mobile-search-title">{title}</span>
            <span className="mobile-search-sources">• {totalSources}来源</span>
          </div>
        </div>
        <ChevronDownIcon
          width={20}
          height={20}
          className={`chevron-icon ${isExpanded ? 'rotated' : ''}`}
        />
      </div>

      {/* 折叠详情 */}
      <div
        className={`mobile-search-collapse ${isExpanded ? 'expanded' : 'collapsed'}`}
      >
        <div style={{ borderTop: '1px solid #f3f4f6' }}>
          {results.map((item, index) => (
            <div key={item.id} className="mobile-search-item">
              {/* 列表项头部 */}
              <div
                className="mobile-search-item-header"
                onClick={() => toggleItem(item.id)}
              >
                <div className="mobile-search-item-content">
                  {/* 状态圆点 */}
                  <div className={`status-dot ${
                    index === 0 ? 'red' :
                    index === 1 ? 'blue' :
                    'green'
                  }`} />

                  {/* 标题 */}
                  <span className="mobile-search-item-title">
                    {item.title}
                  </span>
                </div>

                {/* 展开箭头 */}
                <ChevronDownIcon
                  width={16}
                  height={16}
                  className={`chevron-icon ${
                    expandedItems.has(item.id) ? 'rotated' : ''
                  }`}
                />
              </div>

              {/* 展开内容 */}
              <div
                className={`mobile-search-item-details ${
                  expandedItems.has(item.id) ? 'expanded' : 'collapsed'
                }`}
              >
                <div className="mobile-search-item-details-content">
                  {/* 内容描述 */}
                  {item.content && (
                    <div className="content-description">
                      <p>{item.content}</p>
                    </div>
                  )}

                  {/* 详细信息列表 */}
                  {item.details && item.details.length > 0 && (
                    <div className="details-list">
                      {item.details.map((detail, detailIndex) => (
                        <div key={detailIndex} className="details-item">
                          <div className="details-bullet" />
                          <p className="details-text">{detail}</p>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* 来源链接 */}
                  {item.sources && item.sources.length > 0 && (
                    <div className="sources-section">
                      <p className="sources-title">相关来源:</p>
                      {item.sources.map((source, sourceIndex) => (
                        <div key={sourceIndex} className="source-item">
                          <p className="source-title">{source.title}</p>
                          <p className="source-domain">{source.domain}</p>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 展开按钮 - 当折叠时显示 */}
      {!isExpanded && (
        <div
          className="expand-button"
          onClick={onToggle}
        >
          <span>展开详情</span>
        </div>
      )}
    </div>
  );
};

export default MobileSearchResults;
