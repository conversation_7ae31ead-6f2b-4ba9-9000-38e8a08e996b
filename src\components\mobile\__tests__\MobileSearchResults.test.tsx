import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import MobileSearchResults from '../MobileSearchResults';

// Mock数据
const mockSearchResults = [
  {
    id: 'test-1',
    title: '测试搜索结果',
    content: '这是一个测试内容',
    details: ['详细信息1', '详细信息2'],
    sources: [
      {
        title: '测试来源',
        url: 'https://example.com',
        domain: 'example.com'
      }
    ],
    statusColor: 'green' as const
  }
];

const mockWorkflowNodes = [
  {
    id: 'node-1',
    title: '测试工作流节点',
    status: 'success' as const,
    content: '工作流节点内容',
    details: ['节点详情1', '节点详情2'],
    displayCategory: 'right' as const,
    created_at: Date.now()
  }
];

describe('MobileSearchResults', () => {
  test('渲染基本组件结构', () => {
    render(
      <MobileSearchResults
        title="测试标题"
        totalSources={5}
        results={mockSearchResults}
        isExpanded={false}
      />
    );

    expect(screen.getByText('测试标题')).toBeInTheDocument();
    expect(screen.getByText('• 5来源')).toBeInTheDocument();
    expect(screen.getByText('展开详情')).toBeInTheDocument();
  });

  test('点击头部切换展开状态', () => {
    const mockToggle = jest.fn();
    
    render(
      <MobileSearchResults
        title="测试标题"
        results={mockSearchResults}
        isExpanded={false}
        onToggle={mockToggle}
      />
    );

    const header = screen.getByRole('button', { name: /测试标题/ });
    fireEvent.click(header);
    
    expect(mockToggle).toHaveBeenCalledTimes(1);
  });

  test('展开状态下显示搜索结果', () => {
    render(
      <MobileSearchResults
        title="测试标题"
        results={mockSearchResults}
        isExpanded={true}
      />
    );

    expect(screen.getByText('测试搜索结果')).toBeInTheDocument();
    expect(screen.getByText('折叠详情')).toBeInTheDocument();
  });

  test('点击结果项切换展开状态', () => {
    render(
      <MobileSearchResults
        title="测试标题"
        results={mockSearchResults}
        isExpanded={true}
      />
    );

    const resultHeader = screen.getByText('测试搜索结果');
    fireEvent.click(resultHeader.closest('.result-item-header')!);
    
    // 验证内容是否显示
    expect(screen.getByText('这是一个测试内容')).toBeInTheDocument();
  });

  test('处理工作流节点数据', () => {
    render(
      <MobileSearchResults
        title="工作流测试"
        workflowNodes={mockWorkflowNodes}
        isExpanded={true}
      />
    );

    expect(screen.getByText('测试工作流节点')).toBeInTheDocument();
    expect(screen.getByText('• 1来源')).toBeInTheDocument();
  });

  test('节点点击回调功能', () => {
    const mockNodeClick = jest.fn();
    
    render(
      <MobileSearchResults
        title="测试标题"
        workflowNodes={mockWorkflowNodes}
        isExpanded={true}
        onNodeClick={mockNodeClick}
      />
    );

    const nodeHeader = screen.getByText('测试工作流节点');
    fireEvent.click(nodeHeader.closest('.result-item-header')!);
    
    expect(mockNodeClick).toHaveBeenCalledWith('node-1');
  });

  test('空数据时不渲染组件', () => {
    const { container } = render(
      <MobileSearchResults
        title="空数据测试"
        results={[]}
        workflowNodes={[]}
      />
    );

    expect(container.firstChild).toBeNull();
  });

  test('数据优先级：rightPanelNodes > leftPanelNodes > workflowNodes', () => {
    const rightPanelNodes = [
      {
        id: 'right-1',
        title: '右面板节点',
        status: 'success' as const,
        displayCategory: 'right' as const,
        created_at: Date.now()
      }
    ];

    const leftPanelNodes = [
      {
        id: 'left-1',
        title: '左面板节点',
        status: 'success' as const,
        displayCategory: 'left' as const,
        created_at: Date.now()
      }
    ];

    render(
      <MobileSearchResults
        title="优先级测试"
        workflowNodes={mockWorkflowNodes}
        leftPanelNodes={leftPanelNodes}
        rightPanelNodes={rightPanelNodes}
        isExpanded={true}
      />
    );

    // 应该显示rightPanelNodes的内容
    expect(screen.getByText('右面板节点')).toBeInTheDocument();
    // 不应该显示其他节点
    expect(screen.queryByText('左面板节点')).not.toBeInTheDocument();
    expect(screen.queryByText('测试工作流节点')).not.toBeInTheDocument();
  });

  test('状态颜色映射正确', () => {
    const statusNodes = [
      { ...mockWorkflowNodes[0], id: 'success', status: 'success' as const },
      { ...mockWorkflowNodes[0], id: 'error', status: 'error' as const },
      { ...mockWorkflowNodes[0], id: 'running', status: 'running' as const },
      { ...mockWorkflowNodes[0], id: 'pending', status: 'pending' as const }
    ];

    render(
      <MobileSearchResults
        title="状态测试"
        workflowNodes={statusNodes}
        isExpanded={true}
      />
    );

    const statusDots = document.querySelectorAll('.status-dot');
    expect(statusDots).toHaveLength(4);
    
    // 验证状态颜色类名
    expect(statusDots[0]).toHaveClass('status-dot-green'); // success
    expect(statusDots[1]).toHaveClass('status-dot-red');   // error
    expect(statusDots[2]).toHaveClass('status-dot-blue');  // running
    expect(statusDots[3]).toHaveClass('status-dot-orange'); // pending
  });
});
