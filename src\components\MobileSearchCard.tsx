import React, { useState } from 'react';
import { ChevronDownIcon, SearchIcon } from './icons/Icons';
import './MobileSearchCard.css';

interface SearchResultItem {
  id: string;
  title: string;
  content?: string;
  details?: string[];
  sources?: Array<{
    title: string;
    url: string;
    domain: string;
  }>;
}

interface MobileSearchCardProps {
  title?: string;
  totalSources?: number;
  results: SearchResultItem[];
  className?: string;
}

const MobileSearchCard: React.FC<MobileSearchCardProps> = ({
  title = "DeepSearch",
  totalSources = 34,
  results = [],
  className = ""
}) => {
  const [isMainExpanded, setIsMainExpanded] = useState(false);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  const toggleMainExpansion = () => {
    setIsMainExpanded(!isMainExpanded);
  };

  const toggleItemExpansion = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  const getStatusDotColor = (index: number): string => {
    const colors = ['red', 'blue', 'green', 'orange', 'purple', 'teal'];
    return colors[index % colors.length];
  };

  return (
    <div className={`mobile-search-card ${className}`}>
      {/* 主头部 - 可点击折叠整个卡片 */}
      <div 
        className="mobile-search-card__header"
        onClick={toggleMainExpansion}
      >
        <div className="mobile-search-card__header-content">
          <SearchIcon width={20} height={20} className="mobile-search-card__search-icon" />
          <div className="mobile-search-card__title-section">
            <span className="mobile-search-card__title">{title}</span>
            <span className="mobile-search-card__sources">• {totalSources}来源</span>
          </div>
        </div>
        <ChevronDownIcon
          width={20}
          height={20}
          className={`mobile-search-card__chevron ${isMainExpanded ? 'mobile-search-card__chevron--rotated' : ''}`}
        />
      </div>

      {/* 主要内容区域 - 折叠/展开 */}
      <div className={`mobile-search-card__content ${isMainExpanded ? 'mobile-search-card__content--expanded' : 'mobile-search-card__content--collapsed'}`}>
        <div className="mobile-search-card__content-inner">
          {results.map((item, index) => (
            <div key={item.id} className="mobile-search-card__item">
              {/* 结果项头部 */}
              <div 
                className="mobile-search-card__item-header"
                onClick={() => toggleItemExpansion(item.id)}
              >
                <div className="mobile-search-card__item-header-content">
                  <div className={`mobile-search-card__status-dot mobile-search-card__status-dot--${getStatusDotColor(index)}`} />
                  <span className="mobile-search-card__item-title">{item.title}</span>
                </div>
                <ChevronDownIcon
                  width={16}
                  height={16}
                  className={`mobile-search-card__item-chevron ${expandedItems.has(item.id) ? 'mobile-search-card__item-chevron--rotated' : ''}`}
                />
              </div>

              {/* 结果项详细内容 */}
              <div className={`mobile-search-card__item-details ${expandedItems.has(item.id) ? 'mobile-search-card__item-details--expanded' : 'mobile-search-card__item-details--collapsed'}`}>
                <div className="mobile-search-card__item-details-inner">
                  {/* 内容描述 */}
                  {item.content && (
                    <div className="mobile-search-card__content-description">
                      <p>{item.content}</p>
                    </div>
                  )}

                  {/* 详细信息列表 */}
                  {item.details && item.details.length > 0 && (
                    <div className="mobile-search-card__details-list">
                      {item.details.map((detail, detailIndex) => (
                        <div key={detailIndex} className="mobile-search-card__detail-item">
                          <div className="mobile-search-card__detail-bullet" />
                          <p className="mobile-search-card__detail-text">{detail}</p>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* 来源链接 */}
                  {item.sources && item.sources.length > 0 && (
                    <div className="mobile-search-card__sources-section">
                      <p className="mobile-search-card__sources-title">相关来源:</p>
                      <div className="mobile-search-card__sources-list">
                        {item.sources.map((source, sourceIndex) => (
                          <div key={sourceIndex} className="mobile-search-card__source-item">
                            <p className="mobile-search-card__source-title">{source.title}</p>
                            <p className="mobile-search-card__source-domain">{source.domain}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 展开详情按钮 - 仅在折叠状态显示 */}
      {!isMainExpanded && (
        <div 
          className="mobile-search-card__expand-button"
          onClick={toggleMainExpansion}
        >
          <span>展开详情</span>
        </div>
      )}
    </div>
  );
};

export default MobileSearchCard;
