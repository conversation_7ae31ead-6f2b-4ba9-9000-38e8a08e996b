import { useState, useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { HelmetProvider } from 'react-helmet-async'
import ChatDetail from './components/ChatDetail'
import Home from './components/Home';
import CaseExample from './components/CaseExample';
import I18nRouteWrapper from './components/I18nRouteWrapper';
import I18nTestPage from './components/I18nTestPage';
import SimpleI18nTest from './components/SimpleI18nTest';
import Cookies from 'js-cookie';
import { XAiApi, IGetAiAppInfoResponse } from './api/src/xai-api';
import { DifyApi } from './api/src/dify-api';
import { useMount } from 'ahooks';

function App() {
  const [user, setUser] = useState<string>('nologin')
	const [userInfo, setUserInfo]:any = useState(null)
  const [currentAppUuid, setCurrentAppUuid] = useState<string>('')
  const [appList, setAppList] = useState<IGetAiAppInfoResponse[]>([])

  // 获取域名
  const hostname = window.location.hostname
  let apiBase = 'http://localhost:3000'
  if (hostname === 'ai.medon.com.cn') {
    apiBase = 'https://ai.medon.com.cn/dev-api'
  } else if (hostname === 'ai.medsci.cn') {
    apiBase = 'https://ai.medsci.cn/dev-api'
  }
  const xAiApi = new XAiApi({
    user: user,
    apiBase: apiBase,
    yudaoToken: Cookies.get('yudaoToken') || ''
  });
  const difyApi = new DifyApi({
    user: user,
    apiBase: apiBase,
    yudaoToken: Cookies.get('yudaoToken') || ''
  });

  useMount(() => {
		// 从cookie中获取userInfo
		const userInfoString = Cookies.get('userInfo')
		if (userInfoString) {
      const userInfos = JSON.parse(userInfoString)
			setUserInfo(userInfos)
      setUser(userInfos.username)

			const yudaoToken = Cookies.get('yudaoToken')
			if (yudaoToken) {
				return;
			}
      
      xAiApi.getAiWriteToken({
          userId: userInfos.userId,
          userName: userInfos.userName,
          realName: userInfos.realName,
          avatar: userInfos.avatar,
          plaintextUserId: userInfos.plaintextUserId,
          mobile: userInfos.mobile,
          email: userInfos.email
      }).then((data:any) => {
        if (data?.token) {
          Cookies.set("yudaoToken", data.token);
          localStorage.setItem("hasuraToken", data.htoken);
          localStorage.setItem("openid", data.openid);
          localStorage.setItem("socialUserId", data.socialUserId);
          localStorage.setItem("socialType", data.socialType);
        } else {
          console.error("登录失败: 未返回 token");
        }
      })
		}
	})

	useEffect(() => {
		if (userInfo) {
			setUser(userInfo.userName)
			
      const yudaoToken = Cookies.get('yudaoToken')
			if (yudaoToken) {
				return;
			}
			
			xAiApi.getAiWriteToken({
				userId: userInfo.userId,
				userName: userInfo.userName,
				realName: userInfo.realName,
				avatar: userInfo.avatar,
				plaintextUserId: userInfo.plaintextUserId,
				mobile: userInfo.mobile,
				email: userInfo.email
			}).then((data: any) => {
				if (data?.token) {
					Cookies.set("yudaoToken", data.token);
					localStorage.setItem("hasuraToken", data.htoken);
					localStorage.setItem("openid", data.openid);
					localStorage.setItem("socialUserId", data.socialUserId);
					localStorage.setItem("socialType", data.socialType);
				} else {
					console.error("登录失败: 未返回 token");
				}
			})
		}
	}, [userInfo]) // 依赖 userInfo 和 appservice

	useEffect(() => {
    const yudaoToken = Cookies.get('yudaoToken')
		if (user && yudaoToken) {
			console.log('Updated user====', user)
      const currentApp = appList.find(app => app.appUuid === currentAppUuid);
      xAiApi.updateOptions({
        user: user,
        apiBase: xAiApi.options.apiBase,
        yudaoToken: yudaoToken
      });
      difyApi.updateOptions({
        user: user,
        apiBase: difyApi.options.apiBase,
        yudaoToken: yudaoToken,
        appId: currentApp?.dAppUuid
      });
		}
	}, [user, currentAppUuid]) // 依赖 userId

	useEffect(() => {
		const lang = Cookies.get("ai_apps_lang") || navigator.language || 'zh-CN';
    Cookies.set("ai_apps_lang", lang);
      const fetchAppList = async () => {
        try {
          const novaxs = await xAiApi.getAppByConfigKey({ configKey: 'novax_apps' });
          const elavaxs = await xAiApi.getAppByConfigKey({ configKey: 'elavax_apps' });
          setAppList([...novaxs, ...elavaxs]);
          setCurrentAppUuid(novaxs[0]?.appUuid || elavaxs[0]?.appUuid || '');
        } catch (error) {
          console.error('获取应用列表失败:', error);
        }
      };
    
      fetchAppList();
	}, [])

  return (
    <HelmetProvider>
      <Router>
        <I18nRouteWrapper>
          <Routes>
          {/* 根路径重定向 */}
          <Route path="/" element={<Home
            difyApi={difyApi}
            xAiApi={xAiApi}
            currentAppUuid={currentAppUuid}
            setCurrentAppUuid={setCurrentAppUuid}
            appList={appList}
            setAppList={setAppList} />} />

          {/* 国际化路由结构: /[lang]/[app-name]/[session-id?] */}
          {/* 新对话页面 - 使用特殊标识符 */}
          <Route path="/:lang/:appName/new" element={<ChatDetail />} />

          {/* 案例页面路由 - 使用 cases 复数形式避免冲突 */}
          <Route path="/:lang/cases/:caseId" element={<CaseExample xAiApi={xAiApi} />} />

          {/* 历史对话页面 - 有具体的conversationId */}
          <Route path="/:lang/:appName/:sessionId" element={<ChatDetail />} />

          {/* 应用首页 - 精确匹配，不带末尾斜杠 */}
          <Route path="/:lang/:appName" element={<Home
            difyApi={difyApi}
            xAiApi={xAiApi}
            currentAppUuid={currentAppUuid}
            setCurrentAppUuid={setCurrentAppUuid}
            appList={appList}
            setAppList={setAppList} />} />

          {/* 测试页面 */}
          <Route path="/test/i18n" element={<I18nTestPage />} />
          <Route path="/test/simple" element={<SimpleI18nTest />} />

          {/* 兼容旧路由 */}
          <Route path="/chat" element={<ChatDetail />} />
          <Route path="/chat/:appUuid" element={<ChatDetail />} />
          <Route path="/chat/:appUuid/:conversationId" element={<ChatDetail />} />
          </Routes>
        </I18nRouteWrapper>
      </Router>
    </HelmetProvider>
  );
}

export default App
