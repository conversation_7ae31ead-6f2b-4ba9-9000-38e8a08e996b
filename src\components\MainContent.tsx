import React, { useState, useMemo, useEffect, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import FileUpload, { FileUploadRef } from './file-upload/FileUpload'
import FileIcon from './file-upload/FileIcon'
import Tooltip from './tool-tip/Tooltip'
import { useFileUpload } from './file-upload/useFileUpload'
import { IGetAiAppInfoResponse, PackageByKey } from '../api/src/xai-api';
import { DifyApi, IGetAppParametersResponse } from '../api/src/dify-api';
import { XAiApi } from '../api/src/xai-api';
import { FileTypeMap } from '../api/src/utils/file-type';

import { useI18nRouter, useSimpleTranslation } from '../i18n/simple-hooks';
import { message } from 'antd'


interface MainContentProps {
  onAppSelect: (appUuid: string) => void
  difyApi: DifyApi
  xAiApi: XAiApi
  appList: IGetAiAppInfoResponse[]
  currentApp: IGetAiAppInfoResponse
  appParam?: IGetAppParametersResponse
  appListSub: Map<string, PackageByKey | null>
  preCheck: () => boolean
}

// 定义数据类型
interface ConfigData {
  icon: string;
  title: string;
  desc: string;
  id: string;
}

const MainContent = ({ onAppSelect, difyApi, xAiApi, appList, currentApp, appParam, appListSub, preCheck }: MainContentProps) => {
  const navigate = useNavigate()
  const { currentLanguage, navigateToApp } = useI18nRouter()
  const { t } = useSimpleTranslation()

  // 根据应用名称获取国际化描述
  const getAppDescription = (appNameEn: string): string => {
    switch (appNameEn) {
      case 'novax-base':
        return t('home.inspirationEngine')
      case 'elavax-base':
        return t('home.analysisDescription')
      default:
        return t('home.description')
    }
  }

  const fileUploadRef = useRef<FileUploadRef>(null)
  const [inputValue, setInputValue] = useState('')
  const [showSceneMenu, setShowSceneMenu] = useState(false)
  // 文件状态现在由 useFileUpload Hook 管理
  const [selectedSceneTitle, setSelectedSceneTitle] = useState(t('scene.general'))
  const [suggestedQuestions, setSuggestedQuestions] = useState<ConfigData[]>([])
  const [caseExamples, setCaseExamples] = useState<ConfigData[]>([])
  const subStatusDetail = appListSub.get(currentApp.appNameEn)
  // 从API获取场景分类和案例数据
  useEffect(() => {
    const fetchConfigData = async () => {
      if (!currentApp?.appNameEn) return

      try {
        // 获取开场白
        const questionResponse = await xAiApi.getConfigByKeyFromCache({ configKey: 'suggested_questions' })
        if (questionResponse && typeof questionResponse === 'object') {
          const questions = questionResponse[currentApp.appNameEn]
          if (questions && Array.isArray(questions)) {
            setSuggestedQuestions(questions)
            setSelectedSceneTitle(questions[0].title)
          } else {
            console.log('No questions found for app:', currentApp.appNameEn)
            setSuggestedQuestions([])
            setSelectedSceneTitle(t('scene.general'))
          }
        } else {
          console.log('Invalid questionResponse:', questionResponse)
          setSuggestedQuestions([])
          setSelectedSceneTitle(t('scene.general'))
        }

        // 获取案例数据
        const caseExamplesResponse = await xAiApi.getConfigByKeyFromCache({ configKey: 'case_examples' })
        if (caseExamplesResponse && typeof caseExamplesResponse === 'object') {
          const appCaseExamples = caseExamplesResponse[currentApp.appNameEn]
          if (appCaseExamples && Array.isArray(appCaseExamples)) {
            setCaseExamples(appCaseExamples)
          } else {
            console.log('No case examples found for app:', currentApp.appNameEn)
            setCaseExamples([])
          }
        } else {
          console.log('Invalid caseExamplesResponse:', caseExamplesResponse)
          setCaseExamples([])
        }
      } catch (error) {
        console.error('获取配置数据失败:', error)
        // 如果API调用失败，使用默认数据
        setSuggestedQuestions([])
        setSelectedSceneTitle(t('scene.general'))
        setCaseExamples([])
      }
    }

    fetchConfigData()
  }, [currentApp, xAiApi])



  // 使用 useFileUpload Hook 管理文件上传
  const {
    uploadedFiles: showFiles,
    uploadData: uploadFiles,
    isUploading,
    handleFilesUploaded,
    removeFile
  } = useFileUpload({
    difyApi,
    maxFiles: appParam?.file_upload.number_limits || 1,
    onPreCheck: preCheck,
    onUploadSuccess: (files, uploadData) => {
      console.log('文件上传成功:', files, uploadData)
    }
  })

  const allowedFileTypes = useMemo(() => {
		if (!appParam?.file_upload.enabled) {
			return []
		}
		const result: string[] = []
		appParam.file_upload.allowed_file_types.forEach(item => {
			if (FileTypeMap.get(item)) {
				result.push(...((FileTypeMap.get(item) as string[]) || []))
			}
		})
		return result
	}, [appParam?.file_upload])

  const handleSubmit = () => {
    if (currentApp.appNameEn.includes('pro')) {
      message.success(t('chat.comingSoon'))
      return
    }
    if (inputValue.trim()) {
      const query = inputValue.trim()

      // 使用sessionStorage存储数据，标记为新对话
      const chatData = {
        query,
        appNameEn: currentApp.appNameEn,
        appUuid: currentApp.appUuid,
        isNewConversation: true,
        files: uploadFiles.length > 0 ? uploadFiles : undefined,
        timestamp: Date.now()
      }

      // 使用固定的key存储初始数据
      sessionStorage.setItem('chatInitialData', JSON.stringify(chatData))

      // 直接跳转到新对话页面（使用"new"标识符）
      const targetUrl = `/${currentLanguage}/${currentApp.appNameEn}/new`
      navigate(targetUrl, { replace: true })
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      // Shift+Enter：换行（不提交）
      e.preventDefault()
      handleSubmit()
    }
  }

  const handleSceneSelect = (title: string) => {
    setInputValue(title)
    setSelectedSceneTitle(title) // 更新选中的场景标题
    setShowSceneMenu(false)
  }

  // 处理案例卡片点击事件
  const handleCaseClick = (caseId: string) => {
    navigate(`/${currentLanguage}/cases/${caseId}`)
  };

  return (
    <div className="flex-1 flex flex-col" style={{ backgroundColor: 'var(--bg-main)' }}>
      {/* 主要内容区域 */}
      <div className="flex-1 flex flex-col pt-4 px-4 md:pt-6 md:px-6 lg:pt-8 lg:px-8">
        <div className="flex flex-col items-center justify-start">
        <div className="w-full max-w-6xl">
          {/* 主标题区域 */}
          <div className="text-center mb-6 md:mb-8">
            <div className="flex flex-col items-center gap-2 mb-3">
              <div className="flex items-center gap-3">
                <h2 className="text-xl md:text-2xl font-bold text-gray-900">~{t('home.welcomePrefix')} {currentApp.appName}</h2>
                <div className="w-8 h-8 flex items-center justify-center">
                  <img src={currentApp.appIcon} alt={currentApp.appName} className="w-8 h-8" />
                </div>
              </div>
            </div>
            <p className="text-sm md:text-lg text-gray-600 mb-6 md:mb-10">
              {getAppDescription(currentApp.appNameEn)}
            </p>
          </div>

          {/* 重新设计的输入框区域 */}
          <FileUpload
            ref={fileUploadRef}
            onFilesUploaded={handleFilesUploaded}
            allowedFileTypes={allowedFileTypes}
            disabled={!appParam?.file_upload.enabled}
            isLoading={isUploading}
            maxFiles={appParam?.file_upload.number_limits || 1}
          >
            <div className="relative bg-white shadow-xl mb-6 md:mb-8 rounded-2xl border border-gray-200 hover:shadow-2xl transition-shadow duration-300 ease-in-out">
              {/* 智能体图标和应用名 */}
              <div className="pt-4 px-4">
                <div style={{ display: 'flex', alignItems: 'flex-start', gap: '4px' }}>
                  <img src="https://img.medsci.cn/202507/41d58de3863f47f0a9af7df896412ffd-sTnD3VvQ1426.png" className="w-4 h-4" style={{ marginTop: '4px' }} />
                  <span className="text-blue-600" style={{ whiteSpace: 'nowrap', fontSize: '16px', fontWeight:'500', lineHeight: '1.6' }}>{currentApp.appName}</span>
                </div>

                {/* 文本输入区域 - 第一行跟应用名，第二行和图标对齐 */}
                <div style={{
                  marginTop: '-24px',
                  marginLeft: '0px' // 从最左边开始
                }}>
                  <textarea
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    onKeyDown={handleKeyDown}
                    onFocus={() => {
                      if (!preCheck()) return
                    }}
                    className="w-full border-0 resize-none focus:outline-none focus:border-transparent text-gray-800 placeholder-gray-500 transition-all duration-300 ease-in-out"
                    style={{
                      padding: '0',
                      paddingLeft: '0px', // 左边不留空间
                      minHeight: '140px',
                      fontSize: '16px',
                      lineHeight: '1.6',
                      outline: 'none',
                      border: 'none',
                      boxShadow: 'none',
                      background: 'transparent',
                      textIndent: '115px', // 调整缩进计算
                      marginLeft: '0px' // 确保换行后贴左边
                    }}
                    placeholder={t('chat.inputPlaceholderHome')}
                  />
                </div>
              </div>

              {/* 已上传文件显示 */}
              {showFiles.length > 0 && (
                <div className="px-6 pb-4 pt-2">
                  <div className="flex flex-wrap gap-3">
                    {showFiles.map((file, index) => (
                      <Tooltip
                        key={`${file.name}-${index}`}
                        content={file.name}
                        position="top"
                        delay={300}
                      >
                        <div className="relative group">
                          <div className="flex items-center justify-center w-11 h-11 bg-gray-50 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer">
                            <FileIcon fileName={file.name} size="md" />
                            <button
                              onClick={() => removeFile(index)}
                              className="absolute -top-2 -right-2 w-5 h-5 bg-red-500 text-white rounded-full text-xs hover:bg-red-600 transition-colors flex items-center justify-center opacity-100 md:opacity-0 md:group-hover:opacity-100"
                            >
                              ✕
                            </button>
                          </div>
                        </div>
                      </Tooltip>
                    ))}
                  </div>
                </div>
              )}

              {/* 简化的底部工具栏 - 单排设计 */}
              <div className="flex items-center justify-between p-2 border-gray-100">
                <div className="flex items-center gap-3">
                  {/* 场景选择 */}
                  <div className="relative">
                    <button
                      onClick={() => setShowSceneMenu(!showSceneMenu)}
                      className="flex items-center gap-2 px-4 py-2.5 bg-gray-100 hover:bg-gray-200 rounded-xl text-sm font-medium text-gray-700 transition-colors duration-200 ease-in-out shadow-sm hover:shadow-md scene-select-button" // 添加 scene-select-button 类
                    >
                      <img
                        src={suggestedQuestions.find(s => s.title === selectedSceneTitle)?.icon}
                        alt={selectedSceneTitle}
                        className="w-4 h-4 object-contain"
                      />
                      <span className="hidden sm:inline">{selectedSceneTitle}</span>
                      <span className="sm:hidden">{selectedSceneTitle === t('scene.general') ? t('scene.category') : selectedSceneTitle}</span>
                      <span className={`text-xs transform transition-transform duration-200`}>
                        <svg className={`w-3 h-3 text-gray-500 group-hover:text-indigo-600 transition-colors duration-300 transition-transform ${showSceneMenu ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </span>
                    </button>

                    {/* 移动端全屏场景选择弹出框 */}
                    {showSceneMenu && (
                      <div className="md:hidden mobile-scene-popup fade-in">
                        {/* 移动端头部 */}
                        <div className="flex justify-between items-center mb-4 pb-4 border-b border-gray-200">
                          <h3 className="text-lg font-semibold text-gray-900">{t('scene.selectType')}</h3>
                          <button
                            onClick={() => setShowSceneMenu(false)}
                            className="p-2 text-gray-500 hover:text-gray-700 rounded-lg"
                          >
                            ✕
                          </button>
                        </div>

                        {/* 移动端场景网格 */}
                        <div className="mobile-scene-grid">
                          {suggestedQuestions.map((item) => (
                            <div
                              key={item.title}
                              onClick={() => handleSceneSelect(item.title)}
                              className="mobile-scene-category scene-category"
                            >
                              <div className="scene-icon">
                                <img
                                  src={item.icon}
                                  alt={item.title}
                                  className="w-6 h-6 object-contain"
                                />
                              </div>
                              <div className="scene-title">{item.title}</div>
                              <div className="scene-desc">{item.desc}</div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* 桌面端场景弹出框 */}
                    {showSceneMenu && (
                      <div className="hidden md:block scene-popup fade-in">
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                          {suggestedQuestions.map((item) => (
                            <div
                              key={item.title}
                              onClick={() => handleSceneSelect(item.title)}
                              className="scene-category"
                            >
                              <div className="scene-icon">
                                <img
                                  src={item.icon}
                                  alt={item.title}
                                  className="w-12 h-12 object-contain"
                                />
                              </div>
                              <div className="scene-title">{item.title}</div>
                              <div className="scene-desc">{item.desc}</div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* 文件上传 - 唯一入口 */}
                  {appParam?.file_upload.enabled && (
                  <Tooltip
                    content={
                      <div className="text-center space-y-1.5">
                        <div className="text-xs text-gray-600">
                          <div className="font-medium mb-1">
                            {t('fileUpload.maxFiles').replace('{count}', (appParam?.file_upload.number_limits || 1).toString())}
                          </div>
                          <div className="space-y-1">
                            <div className="text-xs text-gray-500 leading-relaxed">
                              {t('fileUpload.supportedFormats')}: {allowedFileTypes.join(', ')}
                            </div>
                          </div>
                        </div>
                      </div>
                    }
                    position="bottom"
                    delay={300}
                  >
                    <button
                      className="flex items-center gap-2 px-4 py-2.5 bg-gray-100 hover:bg-gray-200 rounded-xl text-sm font-medium text-gray-700 cursor-pointer transition-colors duration-200 ease-in-out shadow-sm hover:shadow-md"
                      onClick={() => {
                        if (!preCheck()) return
                        fileUploadRef.current?.triggerFileSelect()
                      }}
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                      </svg>
                      <span className="hidden sm:inline">{t('chat.uploadFile')}</span>
                      <span className="sm:hidden">{t('common.upload')}</span>
                    </button>
                  </Tooltip>
                  )}
                </div>

                <div className="flex items-center gap-3">
                  <div className="text-xs text-gray-400 hidden sm:block">
                    Enter {t('chat.send')}
                  </div>
                  <button
                    onClick={handleSubmit}
                    disabled={!inputValue.trim() && showFiles.length === 0}
                    className="relative flex items-center justify-center w-10 h-10 rounded-full text-white focus:outline-none focus:ring-2 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed group shadow-lg hover:shadow-xl transition-all duration-200 ease-out transform hover:scale-105 bg-gradient-to-br from-purple-500 to-blue-500 hover:from-purple-400 hover:to-blue-400 focus:ring-purple-500 hover:shadow-purple-500/30"
                  >
                    <div className="flex items-center justify-center">
                      {/* 发送图标 */}
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="group-hover:animate-[takeoff_1.5s_ease-out_infinite] transition-transform duration-200"
                      >
                        <path d="M22 2L11 13"/>
                        <path d="M22 2L15 22L11 13L2 9L22 2Z"/>
                      </svg>
                    </div>
                  </button>
                </div>
              </div>
            </div>
          </FileUpload>

          {/* 智能体模式选择器 - 移动端一排显示，改回简单图标样式 */}
          <div className="grid grid-cols-3 md:grid-cols-5 gap-2 md:gap-4 mb-8 px-2 md:px-0">
            {appList.map((app) => (
              <button
                key={app.appUuid}
                onClick={() => onAppSelect(app.appUuid)}
                className={`relative p-3 md:p-4 rounded-xl border transition-all duration-200 hover:shadow-sm ${
                  currentApp.appUuid === app.appUuid
                    ? 'border-blue-200 bg-blue-25 shadow-sm'
                    : 'border-gray-100 bg-white hover:border-gray-200'
                }`}
                style={currentApp.appUuid === app.appUuid ? { backgroundColor: '#f0f7ff' } : {}}
              >
                {/* 订阅状态V图标 - 左上角，专家样式 */}
                {(appListSub.get(app.appNameEn)?.subStatus === 1 || appListSub.get(app.appNameEn)?.subStatus === 3) && (
                  <div
                    className="absolute top-0 left-0 text-white text-xs px-2 py-1 bg-green-100"
                    style={{
                      borderTopLeftRadius: '12px',
                      borderBottomRightRadius: '8px'
                    }}
                  >
                    <span className="text-green-600 text-sm">✓</span>
                  </div>
                )}
                {/* 专家标签 */}
                <div
                  className={`absolute top-0 right-0 text-white text-xs px-2 py-1 ${
                    currentApp.appUuid === app.appUuid ? 'bg-blue-500' : 'bg-gray-400'
                  }`}
                  style={{
                    borderTopRightRadius: '12px',
                    borderBottomLeftRadius: '8px'
                  }}
                >
                  {app.appNameEn.includes('base') ? t('common.free') : t('common.expert')}
                </div>
                <div className="flex justify-center mb-2">
                  <img src={app.appIcon} alt={app.appName} className="w-6 h-6 md:w-8 md:h-8" />
                </div>
                <div className={`text-xs md:text-sm font-medium text-center ${
                  currentApp.appUuid === app.appUuid ? 'text-blue-600' : 'text-gray-700'
                }`}>
                  {app.appName}
                </div>
              </button>
            ))}
          </div>



          {/* 用户案例展示区域 - 移动端优化 */}
          <div className="mb-6 md:mb-8 px-3 md:px-0">
            <div className="text-center mb-4 md:mb-6">
              <h3 className="text-base md:text-lg font-semibold text-gray-800 mb-2">{t('home.caseShowcase')}</h3>
              <p className="text-xs md:text-sm text-gray-600">{t('home.caseDescription')}</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 md:gap-6">
              {caseExamples.map((caseItem) => (
                <div 
                  key={caseItem.title} 
                  className="case-card" 
                  onClick={() => handleCaseClick(caseItem.id)} // 修改此处，调用 handleCaseClick
                >
                  <div className="case-card-image h-52 md:h-40">
                    <img
                      src={caseItem.icon}
                      alt={caseItem.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <h4 className="font-medium text-gray-900 mb-1 md:mb-2 text-xs md:text-sm">{caseItem.title}</h4>
                  <p className="text-xs text-gray-600 leading-relaxed">{caseItem.desc}</p>
                </div>
              ))}
            </div>
          </div>

          {/* 示例和提示 */}
          <div className="text-center text-gray-500 text-xs md:text-sm pb-6 md:pb-8 px-4 md:px-0">
            <p>{t('home.agentModeDescription')}</p>
          </div>
          </div>
        </div>
      </div>

      {/* 点击外部关闭场景菜单 */}
      {showSceneMenu && (
        <div
          className="fixed inset-0 z-20"
          onClick={() => setShowSceneMenu(false)}
        />
      )}

    </div>
  )
}

export default MainContent
