import React, { useState, useCallback, useEffect, useRef } from 'react';
import { ChevronDownIcon, SearchIcon } from '../icons/Icons';
import './MobileSearchResults.css';

// 搜索结果项数据结构
interface SearchResultItem {
  id: string;
  title: string;
  content?: string;
  details?: string[];
  sources?: Array<{
    title: string;
    url: string;
    domain: string;
  }>;
  statusColor?: 'red' | 'blue' | 'green' | 'orange' | 'purple';
}

// 工作流节点数据结构（兼容现有AssistantMessage）
interface WorkflowNode {
  id: string;
  type: string;
  title: string;
  status: 'running' | 'success' | 'error' | 'pending';
  parallel_id?: string;
  created_at: number;
  error?: string;
  inputs?: any;
  outputs?: any;
  process_data?: any;
  displayCategory?: 'left' | 'right' | 'result' | 'none';
}

// 组件Props接口
interface MobileSearchResultsProps {
  title?: string;
  totalSources?: number;
  results?: SearchResultItem[];
  workflowNodes?: WorkflowNode[];
  leftPanelNodes?: WorkflowNode[];
  rightPanelNodes?: WorkflowNode[];
  isExpanded?: boolean;
  onToggle?: () => void;
  onNodeClick?: (nodeId: string) => void;
  className?: string;
}

const MobileSearchResults: React.FC<MobileSearchResultsProps> = ({
  title = "DeepSearch",
  totalSources = 0,
  results = [],
  workflowNodes = [],
  leftPanelNodes = [],
  rightPanelNodes = [],
  isExpanded = false,
  onToggle,
  onNodeClick,
  className = ""
}) => {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [userManuallyToggled, setUserManuallyToggled] = useState(false);
  const [userManuallyToggledContainer, setUserManuallyToggledContainer] = useState(false);
  const [lastAutoToggleTime, setLastAutoToggleTime] = useState(0);
  const [manualToggleStartTime, setManualToggleStartTime] = useState(0);
  const autoToggleTimeoutRef = useRef<number | null>(null);
  const manualToggleTimeoutRef = useRef<number | null>(null);

  // 处理单个结果项的展开/折叠
  const toggleItem = useCallback((itemId: string, isAutoToggle = false) => {
    if (!isAutoToggle) {
      // 记录用户手动操作
      setUserManuallyToggled(true);
      setManualToggleStartTime(Date.now());

      // 清除之前的恢复定时器
      if (manualToggleTimeoutRef.current) {
        window.clearTimeout(manualToggleTimeoutRef.current);
      }

      // 用户手动操作后，12秒后恢复自动逻辑
      manualToggleTimeoutRef.current = window.setTimeout(() => {
        setUserManuallyToggled(false);
        console.log('🤖 恢复节点自动展开/收缩逻辑');
      }, 12000);

      console.log('👆 用户手动切换节点:', itemId, '- 暂停自动逻辑12秒');
    }

    setExpandedItems(prev => {
      const newExpanded = new Set(prev);
      if (newExpanded.has(itemId)) {
        newExpanded.delete(itemId);
      } else {
        newExpanded.add(itemId);
      }
      return newExpanded;
    });
  }, []);

  // 处理整体容器的展开/折叠
  const handleContainerToggle = useCallback(() => {
    // 记录用户手动操作容器
    setUserManuallyToggledContainer(true);
    setManualToggleStartTime(Date.now());

    // 清除之前的恢复定时器
    if (manualToggleTimeoutRef.current) {
      window.clearTimeout(manualToggleTimeoutRef.current);
    }

    // 用户手动操作容器后，15秒后恢复自动逻辑
    manualToggleTimeoutRef.current = window.setTimeout(() => {
      setUserManuallyToggledContainer(false);
      console.log('🤖 恢复容器自动展开/收缩逻辑');
    }, 15000);

    console.log('👆 用户手动切换容器 - 暂停自动逻辑15秒');

    // 调用原始的切换函数
    if (onToggle) {
      onToggle();
    }
  }, [onToggle]);

  // 将工作流节点转换为搜索结果项格式
  const convertWorkflowNodesToResults = useCallback((nodes: WorkflowNode[]): SearchResultItem[] => {
    return nodes.map(node => {
      // 从outputs中提取内容和详细信息
      const content = node.outputs?.answer || node.outputs?.result || '';
      const details: string[] = [];

      // 尝试从不同字段提取详细信息
      if (node.outputs?.details && Array.isArray(node.outputs.details)) {
        details.push(...node.outputs.details);
      }
      if (node.outputs?.steps && Array.isArray(node.outputs.steps)) {
        details.push(...node.outputs.steps);
      }
      if (node.process_data && typeof node.process_data === 'object') {
        Object.entries(node.process_data).forEach(([key, value]) => {
          if (typeof value === 'string' && value.length > 0) {
            details.push(`${key}: ${value}`);
          }
        });
      }

      // 尝试从outputs中提取来源信息
      const sources: Array<{title: string; url: string; domain: string}> = [];
      if (node.outputs?.sources && Array.isArray(node.outputs.sources)) {
        sources.push(...node.outputs.sources.map((source: any) => ({
          title: source.title || source.name || '未知来源',
          url: source.url || source.link || '#',
          domain: source.domain || (source.url ? new URL(source.url).hostname : 'unknown')
        })));
      }

      return {
        id: node.id,
        title: node.title,
        content,
        details: details.length > 0 ? details : undefined,
        sources: sources.length > 0 ? sources : undefined,
        statusColor: getStatusColor(node.status)
      };
    });
  }, []);

  // 根据节点状态获取状态颜色
  const getStatusColor = (status: string): 'red' | 'blue' | 'green' | 'orange' | 'purple' => {
    switch (status) {
      case 'success': return 'green';
      case 'error': return 'red';
      case 'running': return 'blue';
      case 'pending': return 'orange';
      default: return 'purple';
    }
  };

  // 获取状态圆点CSS类名
  const getStatusColorClass = (color?: string) => {
    switch (color) {
      case 'red': return 'status-dot-red';
      case 'blue': return 'status-dot-blue';
      case 'green': return 'status-dot-green';
      case 'orange': return 'status-dot-orange';
      case 'purple': return 'status-dot-purple';
      default: return 'status-dot-blue';
    }
  };

  // 合并所有数据源
  const allResults = React.useMemo(() => {
    const combinedResults = [...results];

    // 优先显示rightPanelNodes，然后是leftPanelNodes，最后是workflowNodes
    const nodesToProcess = rightPanelNodes.length > 0
      ? rightPanelNodes
      : leftPanelNodes.length > 0
        ? leftPanelNodes
        : workflowNodes;

    const convertedNodes = convertWorkflowNodesToResults(nodesToProcess);
    combinedResults.push(...convertedNodes);

    return combinedResults;
  }, [results, workflowNodes, leftPanelNodes, rightPanelNodes, convertWorkflowNodesToResults]);

  // 智能展开/收缩逻辑
  useEffect(() => {
    // 如果用户最近手动操作过节点或容器，则不执行自动逻辑
    if (userManuallyToggled || userManuallyToggledContainer) {
      console.log('⏸️ 自动逻辑已暂停 - 用户手动操作中');
      return;
    }

    // 清除之前的定时器
    if (autoToggleTimeoutRef.current) {
      window.clearTimeout(autoToggleTimeoutRef.current);
    }

    // 延迟执行自动逻辑，避免频繁切换
    autoToggleTimeoutRef.current = window.setTimeout(() => {
      const currentTime = Date.now();

      // 避免过于频繁的自动切换（最少间隔500ms）
      if (currentTime - lastAutoToggleTime < 500) {
        return;
      }

      // 获取当前所有工作流节点
      const allWorkflowNodes = [
        ...rightPanelNodes,
        ...leftPanelNodes,
        ...workflowNodes
      ];

      // 查找正在运行的节点
      const runningNodes = allWorkflowNodes.filter(node => node.status === 'running');
      const hasRunningNodes = runningNodes.length > 0;

      // 1. 如果有正在运行的节点，自动展开容器和运行中的节点
      if (hasRunningNodes) {
        console.log('🔄 检测到运行中的节点:', runningNodes.map(n => n.title));

        // 展开整体容器（仅在用户未手动操作容器时）
        if (!isExpanded && onToggle && !userManuallyToggledContainer) {
          console.log('📂 自动展开容器');
          onToggle();
        }

        // 展开所有运行中的节点（仅在用户未手动操作节点时）
        if (!userManuallyToggled) {
          setExpandedItems(prev => {
            const newExpanded = new Set(prev);
            let hasNewExpansion = false;
            runningNodes.forEach(node => {
              if (!newExpanded.has(node.id)) {
                newExpanded.add(node.id);
                hasNewExpansion = true;
                console.log('📂 自动展开运行中节点:', node.title);
              }
            });
            return newExpanded;
          });
        }

        setLastAutoToggleTime(currentTime);
      }
      // 2. 如果没有运行中的节点，但有已完成的节点，收缩运行完成的节点
      else if (allWorkflowNodes.length > 0) {
        // 查找之前运行中但现在已完成的节点
        const completedNodes = allWorkflowNodes.filter(node =>
          node.status === 'success' || node.status === 'error'
        );

        console.log('✅ 检测到已完成的节点:', completedNodes.map(n => `${n.title}(${n.status})`));

        // 收缩已完成的节点（仅在用户未手动操作节点时）
        if (!userManuallyToggled && completedNodes.length > 0) {
          setExpandedItems(prev => {
            const newExpanded = new Set(prev);
            let hasCollapse = false;
            completedNodes.forEach(node => {
              if (newExpanded.has(node.id)) {
                newExpanded.delete(node.id);
                hasCollapse = true;
                console.log('📁 自动收缩已完成节点:', node.title);
              }
            });
            return newExpanded;
          });
        }

        // 如果所有节点都完成了，延迟收缩整体容器
        if (completedNodes.length === allWorkflowNodes.length && isExpanded && !userManuallyToggledContainer) {
          console.log('🏁 所有节点已完成，2秒后自动收缩容器');
          window.setTimeout(() => {
            if (onToggle && !userManuallyToggledContainer) {
              console.log('📁 自动收缩容器');
              onToggle();
            }
          }, 2000); // 2秒后自动收缩容器，给用户时间查看结果
        }

        setLastAutoToggleTime(currentTime);
      }
    }, 500); // 500ms延迟执行

    // 清理函数
    return () => {
      if (autoToggleTimeoutRef.current) {
        window.clearTimeout(autoToggleTimeoutRef.current);
      }
    };
  }, [
    rightPanelNodes,
    leftPanelNodes,
    workflowNodes,
    isExpanded,
    onToggle,
    userManuallyToggled,
    userManuallyToggledContainer,
    lastAutoToggleTime
  ]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (manualToggleTimeoutRef.current) {
        window.clearTimeout(manualToggleTimeoutRef.current);
      }
    };
  }, []);

  // 计算实际来源数量
  const actualSourceCount = totalSources || allResults.length;

  // 如果没有任何数据，不渲染组件
  if (allResults.length === 0) {
    return null;
  }

  return (
    <div className={`mobile-search-results ${className}`}>
      {/* 主容器头部 */}
      <div className="search-results-header" onClick={handleContainerToggle}>
        <div className="header-left">
          <SearchIcon className="search-icon" />
          <span className="search-title">{title}</span>
          <span className="source-count">• {actualSourceCount}来源</span>
        </div>
        <ChevronDownIcon
          className={`chevron-icon ${isExpanded ? 'expanded' : ''}`}
        />
      </div>
      {/* 搜索结果列表 */}
      <div className={`results-container ${isExpanded ? 'collapsed' : 'expanded'}`}>
        <div className="results-content">
          {allResults.map((item) => (
            <div key={item.id} className="result-item">
              <div
                className="result-item-header"
                onClick={() => {
                  toggleItem(item.id);
                  // 如果有节点点击回调，也触发它
                  if (onNodeClick) {
                    onNodeClick(item.id);
                  }
                }}
              >
                <div className="result-header-left">
                  <div className={`status-dot ${getStatusColorClass(item.statusColor)}`}></div>
                  <span className="result-title">{item.title}</span>
                </div>
                <ChevronDownIcon
                  className={`result-chevron ${expandedItems.has(item.id) ? 'expanded' : ''}`}
                />
              </div>

              {/* 结果项内容 */}
              <div className={`result-item-content ${expandedItems.has(item.id) ? 'expanded' : 'collapsed'}`}>
                <div className="result-content-inner">
                  {item.content && (
                    <div className="result-description">
                      {item.content}
                    </div>
                  )}

                  {item.details && item.details.length > 0 && (
                    <div className="result-details">
                      <ul className="details-list">
                        {item.details.map((detail, detailIndex) => (
                          <li key={detailIndex} className="detail-item">
                            {detail}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {item.sources && item.sources.length > 0 && (
                    <div className="result-sources">
                      <div className="sources-title">来源链接：</div>
                      {item.sources.map((source, sourceIndex) => (
                        <div key={sourceIndex} className="source-item">
                          <a
                            href={source.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="source-link"
                          >
                            <div className="source-title">{source.title}</div>
                            <div className="source-domain">{source.domain}</div>
                          </a>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default MobileSearchResults;
