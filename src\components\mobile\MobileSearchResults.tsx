import React, { useState } from 'react';
import { ChevronDownIcon, SearchIcon } from '../icons/Icons';
import './MobileSearchResults.css';

interface SearchResultItem {
  id: string;
  title: string;
  content?: string;
  details?: string[];
  sources?: Array<{
    title: string;
    url: string;
    domain: string;
  }>;
  statusColor?: 'red' | 'blue' | 'green' | 'orange' | 'purple';
}

interface MobileSearchResultsProps {
  title?: string;
  totalSources?: number;
  results: SearchResultItem[];
  isExpanded?: boolean;
  onToggle?: () => void;
  className?: string;
}

const MobileSearchResults: React.FC<MobileSearchResultsProps> = ({
  title = "DeepSearch",
  totalSources = 34,
  results = [],
  isExpanded = false,
  onToggle,
  className = ""
}) => {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  const toggleItem = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  const getStatusColorClass = (color?: string) => {
    switch (color) {
      case 'red': return 'status-dot-red';
      case 'blue': return 'status-dot-blue';
      case 'green': return 'status-dot-green';
      case 'orange': return 'status-dot-orange';
      case 'purple': return 'status-dot-purple';
      default: return 'status-dot-blue';
    }
  };

  return (
    <div className={`mobile-search-results ${className}`}>
      {/* 主容器头部 */}
      <div className="search-results-header" onClick={onToggle}>
        <div className="header-left">
          <SearchIcon className="search-icon" />
          <span className="search-title">{title}</span>
          <span className="source-count">• {totalSources}来源</span>
        </div>
        <ChevronDownIcon 
          className={`chevron-icon ${isExpanded ? 'expanded' : ''}`}
        />
      </div>

      {/* 展开状态提示 */}
      <div className="expand-status">
        {isExpanded ? '折叠详情' : '展开详情'}
      </div>

      {/* 搜索结果列表 */}
      <div className={`results-container ${isExpanded ? 'expanded' : 'collapsed'}`}>
        <div className="results-content">
          {results.map((item, index) => (
            <div key={item.id} className="result-item">
              <div 
                className="result-item-header"
                onClick={() => toggleItem(item.id)}
              >
                <div className="result-header-left">
                  <div className={`status-dot ${getStatusColorClass(item.statusColor)}`}></div>
                  <span className="result-title">{item.title}</span>
                </div>
                <ChevronDownIcon 
                  className={`result-chevron ${expandedItems.has(item.id) ? 'expanded' : ''}`}
                />
              </div>

              {/* 结果项内容 */}
              <div className={`result-item-content ${expandedItems.has(item.id) ? 'expanded' : 'collapsed'}`}>
                <div className="result-content-inner">
                  {item.content && (
                    <div className="result-description">
                      {item.content}
                    </div>
                  )}

                  {item.details && item.details.length > 0 && (
                    <div className="result-details">
                      <ul className="details-list">
                        {item.details.map((detail, detailIndex) => (
                          <li key={detailIndex} className="detail-item">
                            {detail}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {item.sources && item.sources.length > 0 && (
                    <div className="result-sources">
                      <div className="sources-title">来源链接：</div>
                      {item.sources.map((source, sourceIndex) => (
                        <div key={sourceIndex} className="source-item">
                          <a 
                            href={source.url} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="source-link"
                          >
                            <div className="source-title">{source.title}</div>
                            <div className="source-domain">{source.domain}</div>
                          </a>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default MobileSearchResults;
