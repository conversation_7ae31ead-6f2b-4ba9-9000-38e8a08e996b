// AiResponseRenderer.tsx
import React, {
  FC,
  useRef,
  useState,
  useEffect,
  ReactNode,
  useMemo,
  useCallback
} from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import 'katex/dist/katex.min.css';
import { BlockMath, InlineMath } from 'react-katex';
import mermaid from 'mermaid';
import type { Components } from 'react-markdown';

// Emoji 处理工具函数
const processEmojis = (text: string): string => {
  // 只处理基础的文本表情符号，不处理中文词汇
  const emojiMap: { [key: string]: string } = {
    // 基础表情符号
    '☺': '☺️',
    ':)': '😊',
    ':-)': '😊',
    ':(': '😢',
    ':-(': '😢',
    ':D': '😃',
    ':-D': '😃',
    ';)': '😉',
    ';-)': '😉',
    ':P': '😛',
    ':-P': '😛',
    ':p': '😛',
    ':-p': '😛',
    ':o': '😮',
    ':-o': '😮',
    ':O': '😱',
    ':-O': '😱',
    ':|': '😐',
    ':-|': '😐',
    ':*': '😘',
    ':-*': '😘',
    '<3': '❤️',
    '</3': '💔',

    // 只处理特定的符号转换，不处理中文词汇
    '~': '～',
    '。。。': '…',
    '...': '…'
  };

  let processedText = text;

  // 只处理表情符号，使用精确匹配
  Object.entries(emojiMap).forEach(([textEmoji, emoji]) => {
    // 对于特殊字符，需要转义
    const escapedText = textEmoji.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

    // 对于表情符号，使用更简单和安全的匹配方式
    if (textEmoji.match(/^[:\-\(\)\[\]<>3pPdDoO\|*]+$/)) {
      // 表情符号需要边界匹配，避免在单词中间替换
      // 使用简单的边界检查，避免复杂的正则表达式
      const parts = processedText.split(textEmoji);
      if (parts.length > 1) {
        processedText = parts.join(emoji);
      }
    } else if (textEmoji === '~') {
      // 只在句末或独立使用时替换波浪号
      processedText = processedText.replace(/~(?=\s|$)/g, emoji);
    } else if (textEmoji === '。。。' || textEmoji === '...') {
      // 省略号直接替换
      const regex = new RegExp(escapedText, 'g');
      processedText = processedText.replace(regex, emoji);
    }
  });

  return processedText;
};

// Mermaid初始化状态
let mermaidInitialized = false;

// Mermaid图表组件
interface MermaidDiagramProps {
  code: string;
}

const MermaidDiagram: FC<MermaidDiagramProps> = ({ code }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [svg, setSvg] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // 确保Mermaid只初始化一次
    if (!mermaidInitialized) {
      try {
        mermaid.initialize({ 
          startOnLoad: false, 
          theme: 'dark',
          securityLevel: 'loose',
          fontFamily: 'inherit'
        });
        mermaidInitialized = true;
      } catch (initError) {
        console.error("Mermaid initialization error:", initError);
        setError("Failed to initialize Mermaid");
        return;
      }
    }

    // 清除之前的内容
    setSvg(null);
    setError(null);
    
    const container = containerRef.current;
    if (!container) return;

    const id = `mermaid-${Math.random().toString(36).substring(2, 11)}`;
    container.id = id;

    // 异步渲染图表
    const renderDiagram = async () => {
      try {
        const { svg } = await mermaid.render(id, code);
        setSvg(svg);
      } catch (renderError:any) {
        console.error("Mermaid render error:", renderError);
        setError(renderError.message);
      }
    };

    renderDiagram();
  }, [code]);

  if (error) {
    return (
      <div className="mermaid-error">
        <p>Failed to render diagram:</p>
        <pre>{error}</pre>
        <details>
          <summary>View source</summary>
          <pre>{code}</pre>
        </details>
      </div>
    );
  }

  return (
    <div ref={containerRef} className="mermaid-container">
      {!svg && <div className="mermaid-loading">Rendering diagram...</div>}
      {svg && <div dangerouslySetInnerHTML={{ __html: svg }} />}
    </div>
  );
};

// 错误边界组件
interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, { hasError: boolean }> {
  state = { hasError: false };

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error, info: React.ErrorInfo) {
    console.error("Component render error:", error, info);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || <div className="render-error">Component render failed</div>;
    }
    return this.props.children;
  }
}

// 主渲染器组件
interface AiResponseRendererProps {
  content: string;
  fontSize?: 'sm' | 'base' | 'lg'; // 新增：字体大小选项
}

const AiResponseRenderer: FC<AiResponseRendererProps> = ({ content, fontSize = 'base' }) => {
  // 清理内容中的多余空白和换行 - 特别针对WorkflowNodeItem的whitespace-pre-wrap问题
  const cleanContent = (text: string): string => {
    // 首先进行基础清理
    let cleaned = text.trim()

    // 特别处理表格前的大量空行 - 使用更激进的清理策略
    cleaned = cleaned.replace(/(\n\s*){100,}(\|)/g, '\n\n$2') // 100+空行 → 2个换行
    cleaned = cleaned.replace(/(\n\s*){50,}(\|)/g, '\n\n$2')  // 50+空行 → 2个换行
    cleaned = cleaned.replace(/(\n\s*){30,}(\|)/g, '\n\n$2')  // 30+空行 → 2个换行
    cleaned = cleaned.replace(/(\n\s*){20,}(\|)/g, '\n\n$2')  // 20+空行 → 2个换行
    cleaned = cleaned.replace(/(\n\s*){10,}(\|)/g, '\n\n$2')  // 10+空行 → 2个换行
    cleaned = cleaned.replace(/(\n\s*){5,}(\|)/g, '\n\n$2')   // 5+空行 → 2个换行
    cleaned = cleaned.replace(/(\n\s*){3,}(\|)/g, '\n\n$2')   // 3+空行 → 2个换行

    // 处理任何形式的大量空白字符
    cleaned = cleaned.replace(/\s{20,}/g, ' ')  // 将20+连续空格替换为单个空格
    cleaned = cleaned.replace(/\s{10,}/g, ' ')  // 将10+连续空格替换为单个空格

    // 然后进行常规清理
    cleaned = cleaned
      .replace(/\n{5,}/g, '\n\n')     // 将5个或更多连续换行替换为2个换行
      .replace(/\n{3,}/g, '\n\n')     // 将3个或更多连续换行替换为2个换行
      .replace(/^\s*\n/gm, '\n')      // 去除行首的空白字符
      .replace(/\n\s*$/gm, '\n')      // 去除行尾的空白字符
      .replace(/\n\s+\n/g, '\n\n')    // 去除换行之间的空白字符
      .replace(/(\|.*\|)\n+/g, '$1\n') // 确保表格后只有一个换行

    // 最后的安全检查：确保表格前没有超过2个换行
    cleaned = cleaned.replace(/\n{2,}(\|)/g, '\n\n$1')

    return cleaned
  }

  // 移除复杂的状态管理，直接渲染内容以避免闪烁
  // const [visibleContent, setVisibleContent] = useState('');
  // const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // useEffect(() => {
  //   // 初始渲染部分内容（前5000字符）
  //   setVisibleContent(content.slice(0, 5000));
  //
  //   // 如果内容较长，延迟渲染剩余部分
  //   if (content.length > 5000) {
  //     if (timeoutRef.current) {
  //       clearTimeout(timeoutRef.current);
  //     }
  //
  //     timeoutRef.current = setTimeout(() => {
  //       setVisibleContent(content);
  //     }, 300);
  //   } else {
  //     setVisibleContent(content);
  //   }

  //   return () => {
  //     if (timeoutRef.current) {
  //       clearTimeout(timeoutRef.current);
  //     }
  //   };
  // }, [content]);

  // 处理文本中的LaTeX公式
  const processLatex = useCallback((text: string): ReactNode[] => {
    // 处理块级公式 $$...$$ 和内联公式 $...$
    const parts = text.split(/(\$\$.*?\$\$|\$.*?\$)/g);
    return parts.map((part, index) => {
      if (part.startsWith('$$') && part.endsWith('$$')) {
        try {
          return <BlockMath key={index} math={part.slice(2, -2).trim()} />;
        } catch (error) {
          return (
            <span key={index} className="latex-error">
              {part}
            </span>
          );
        }
      } else if (part.startsWith('$') && part.endsWith('$')) {
        try {
          return <InlineMath key={index} math={part.slice(1, -1).trim()} />;
        } catch (error) {
          return (
            <span key={index} className="latex-error">
              {part}
            </span>
          );
        }
      }
      return part;
    });
  }, []);

  // 使用useMemo优化渲染器配置
  const renderers = useMemo<Components>(() => ({
    // 段落渲染器 - 支持 Emoji 和 LaTeX
    p: ({ children }) => {
      const processedChildren = React.Children.map(children, child => {
        if (typeof child === 'string') {
          // 先处理 emoji，再处理 LaTeX
          const emojiProcessed = processEmojis(child);
          return processLatex(emojiProcessed);
        }
        return child;
      });

      return <p className="emoji-support my-2 md:my-1">{processedChildren}</p>;
    },

    // 文本渲染器 - 支持 Emoji 和 LaTeX
    text: ({ children }) => {
      const textContent = String(children);
      // 先处理 emoji，再处理 LaTeX
      const emojiProcessed = processEmojis(textContent);
      const processedContent = processLatex(emojiProcessed);
      return <span className="emoji-support">{processedContent}</span>;
    },

    // 代码块渲染器
    code: ({ children, className, ref, ...props }) => {
      const codeContent = String(children).trim();
      const match = /language-(\w+)/.exec(className || '');
      const language = match?.[1];
      const inline = (props as any)?.inline;
      
      // Mermaid图表处理
      if (language === 'mermaid') {
        return (
          <ErrorBoundary fallback={
            <div className="mermaid-error">
              <pre>Failed to render Mermaid diagram</pre>
              <details>
                <summary>View source</summary>
                <pre>{codeContent}</pre>
              </details>
            </div>
          }>
            <MermaidDiagram code={codeContent} />
          </ErrorBoundary>
        );
      }

      // LaTeX块处理
      if (language === 'latex' || language === 'tex') {
        return (
          <ErrorBoundary fallback={
            <div className="latex-error-block">
              <SyntaxHighlighter language="text" style={vscDarkPlus as any}>
                {`LaTeX syntax error in block:\n\n${codeContent}`}
              </SyntaxHighlighter>
            </div>
          }>
            <BlockMath math={codeContent} />
          </ErrorBoundary>
        );
      }

      // 内联代码处理
      if (inline) {
        return <code className="inline-code" ref={ref} {...props}>{children}</code>;
      }

      // 语法高亮处理
      return (
        <SyntaxHighlighter
          style={vscDarkPlus as any}
          language={language || 'text'}
          PreTag="div"
          showLineNumbers={!inline && (language || '').length > 0}
          wrapLongLines
          {...props}
        >
          {codeContent}
        </SyntaxHighlighter>
      );
    },

    // 表格渲染器
    table: ({ children }) => (
      <div className="table-container">
        <table>{children}</table>
      </div>
    ),

    // 图片渲染器
    img: ({ src, alt, ...props }) => (
      <div className="image-container">
        <img 
          src={src} 
          alt={alt || 'Image'} 
          loading="lazy"
          {...props} 
        />
        {alt && <div className="image-caption">{alt}</div>}
      </div>
    )
  }), [processLatex]);

  // 根据 fontSize 属性设置字体大小类名
  const fontSizeClass = fontSize === 'lg' ? 'text-base' : fontSize === 'base' ? 'text-sm' : 'text-xs'

  return (
    <div className={`ai-response-container max-w-4xl ${fontSizeClass}`}>
      <ErrorBoundary fallback={
        <div className="render-error">
          Failed to render content. The response may contain unsupported formatting.
        </div>
      }>
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeRaw]}
          components={renderers}
          skipHtml={false}
        >
          {cleanContent(content)}
        </ReactMarkdown>
      </ErrorBoundary>
    </div>
  );
};

export default React.memo(AiResponseRenderer);