/* 移动端搜索结果组件样式 */

/* 主容器样式 */
.mobile-search-container {
  width: 100%;
  max-width: 28rem; /* max-w-md */
  margin: 0 auto;
  background-color: white;
  border-radius: 1rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

/* 头部样式 */
.mobile-search-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.mobile-search-header:hover {
  background-color: #f9fafb;
}

.mobile-search-header-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.mobile-search-title {
  font-weight: 500;
  color: #111827;
}

.mobile-search-sources {
  color: #6b7280;
  font-size: 0.875rem;
  margin-left: 0.5rem;
}

/* 折叠内容容器 */
.mobile-search-collapse {
  overflow: hidden;
  transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-search-collapse.expanded {
  max-height: 100vh; /* 使用视口高度确保足够空间 */
}

.mobile-search-collapse.collapsed {
  max-height: 0;
}

/* 列表项样式 */
.mobile-search-item {
  border-bottom: 1px solid #f3f4f6;
}

.mobile-search-item:last-child {
  border-bottom: none;
}

.mobile-search-item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.mobile-search-item-header:hover {
  background-color: #f9fafb;
}

.mobile-search-item-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
  min-width: 0;
}

/* 状态圆点 */
.status-dot {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-dot.red {
  background-color: #ef4444;
}

.status-dot.blue {
  background-color: #3b82f6;
}

.status-dot.green {
  background-color: #10b981;
}

/* 项目标题 */
.mobile-search-item-title {
  color: #1f2937;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 展开箭头 */
.chevron-icon {
  color: #9ca3af;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
}

.chevron-icon.rotated {
  transform: rotate(180deg);
}

/* 项目详细内容 */
.mobile-search-item-details {
  overflow: hidden;
  transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-search-item-details.expanded {
  max-height: 50rem; /* 800px - 给足够的空间 */
}

.mobile-search-item-details.collapsed {
  max-height: 0;
}

.mobile-search-item-details-content {
  padding: 0 1rem 1rem 1rem;
}

/* 内容描述框 */
.content-description {
  background-color: #f9fafb;
  border-radius: 0.5rem;
  padding: 0.75rem;
  margin-bottom: 0.75rem;
}

.content-description p {
  color: #374151;
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0;
}

/* 详细信息列表 */
.details-list {
  margin-bottom: 0.75rem;
}

.details-item {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.details-item:last-child {
  margin-bottom: 0;
}

.details-bullet {
  width: 0.375rem;
  height: 0.375rem;
  border-radius: 50%;
  background-color: #9ca3af;
  margin-top: 0.5rem;
  flex-shrink: 0;
}

.details-text {
  color: #4b5563;
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0;
}

/* 来源链接 */
.sources-section {
  margin-top: 0.75rem;
}

.sources-title {
  color: #6b7280;
  font-size: 0.75rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.source-item {
  background-color: #eff6ff;
  border-radius: 0.5rem;
  padding: 0.5rem;
  margin-bottom: 0.5rem;
}

.source-item:last-child {
  margin-bottom: 0;
}

.source-title {
  color: #1e40af;
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 0 0 0.25rem 0;
}

.source-domain {
  color: #2563eb;
  font-size: 0.75rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 0;
}

/* 展开按钮 */
.expand-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem;
  color: #6b7280;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-top: 1px solid #e5e7eb;
}

.expand-button:hover {
  background-color: #f9fafb;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .mobile-search-container {
    margin: 0 0.5rem;
    max-width: calc(100% - 1rem);
  }
  
  .mobile-search-header,
  .mobile-search-item-header {
    padding: 0.875rem;
  }
  
  .mobile-search-item-details-content {
    padding: 0 0.875rem 0.875rem 0.875rem;
  }
}

/* 平滑滚动 */
.mobile-search-collapse,
.mobile-search-item-details {
  scroll-behavior: smooth;
}

/* 焦点样式 */
.mobile-search-header:focus,
.mobile-search-item-header:focus,
.expand-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: -2px;
}

/* 禁用选择文本（对于可点击元素） */
.mobile-search-header,
.mobile-search-item-header,
.expand-button {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}
