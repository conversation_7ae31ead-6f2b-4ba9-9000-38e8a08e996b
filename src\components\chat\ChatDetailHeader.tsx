import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeftIcon } from '../icons/Icons';
import { useSimpleTranslation, useI18nRouter } from '../../i18n/simple-hooks';

interface ChatDetailHeaderProps {
  title?: string;
  showBackButton?: boolean;
}

/**
 * 聊天详情页头部组件
 * 包含返回按钮和页面标题
 */
const ChatDetailHeader: React.FC<ChatDetailHeaderProps> = ({ 
  title = 'NovaX AI', 
  showBackButton = true 
}) => {
  const { t } = useSimpleTranslation();
  const { navigateToApp } = useI18nRouter();
  const navigate = useNavigate();
  const { lang, appName } = useParams<{
    lang?: string;
    appName?: string;
  }>();

  // 返回功能
  const handleGoBack = () => {
    // 如果有应用名称，返回到应用首页
    if (appName) {
      navigateToApp(appName);
    } else {
      // 否则返回到主页
      navigateToApp('novax-base');
    }
  };

  return (
    <div className="sticky top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-md border-b border-gray-200/50 px-4 sm:px-6 py-3 shadow-sm">
      <div className="flex items-center justify-between max-w-6xl mx-auto">
        <div className="flex items-center gap-3">
          {/* 返回按钮 */}
          {showBackButton && (
            <button
              onClick={handleGoBack}
              className="
                flex items-center justify-center w-9 h-9
                text-gray-600 hover:text-gray-800
                bg-gray-50 hover:bg-gray-100
                rounded-lg border border-gray-200
                transition-all duration-200
                hover:scale-105 active:scale-95
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50
              "
              title={t('common.back')}
            >
              <ArrowLeftIcon
                width={18}
                height={18}
                stroke="currentColor"
                strokeWidth={2}
              />
            </button>
          )}

          {/* 应用标题 */}
          <div className="flex items-center gap-2">
            <div className="w-7 h-7 bg-gradient-to-br from-gray-800 to-black rounded-lg flex items-center justify-center shadow-sm">
              <span className="text-white text-sm font-bold">N</span>
            </div>
            <span className="font-semibold text-gray-900 text-lg">{title}</span>
          </div>
        </div>

        {/* 右侧操作区域 */}
        <div className="flex items-center gap-2">
          {/* 可以在这里添加其他操作按钮，如分享、设置等 */}
          <button className="p-2 text-gray-500 hover:text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200">
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <circle cx="12" cy="12" r="1"></circle>
              <circle cx="19" cy="12" r="1"></circle>
              <circle cx="5" cy="12" r="1"></circle>
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatDetailHeader;
