import React, { useState, useEffect } from 'react'
import { usePara<PERSON>, useNavigate } from 'react-router-dom'
import FloatingButtons from './FloatingButtons'
import { XAiApi } from '../api/src/xai-api'
import { message } from 'antd'
import { useSimpleTranslation } from '../i18n/simple-hooks'
import AiResponseRenderer from './AiResponseRenderer'

interface ChatMessage {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: Date
  isGenerating?: boolean
}

interface QAItem {
  query: string
  answer: string
}

interface CaseExampleProps {
  xAiApi: XAiApi
}

// 打字机效果组件
const TypewriterText: React.FC<{
  text: string
  speed?: number
  onComplete?: () => void
  delay?: number
}> = ({ text, speed = 50, onComplete, delay = 0 }) => {
  const [displayText, setDisplayText] = useState('')
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isStarted, setIsStarted] = useState(false)

  useEffect(() => {
    if (delay > 0) {
      const delayTimer = setTimeout(() => {
        setIsStarted(true)
      }, delay)
      return () => clearTimeout(delayTimer)
    } else {
      setIsStarted(true)
    }
  }, [delay])

  useEffect(() => {
    if (!isStarted || currentIndex >= text.length) {
      if (currentIndex >= text.length && onComplete) {
        onComplete()
      }
      return
    }

    const timer = setTimeout(() => {
      setDisplayText(prev => prev + text[currentIndex])
      setCurrentIndex(prev => prev + 1)
    }, speed)

    return () => clearTimeout(timer)
  }, [currentIndex, text, speed, onComplete, isStarted])

  return (
    <span>
      {displayText}
      {currentIndex < text.length && (
        <span className="inline-block w-0.5 h-4 bg-gray-600 ml-0.5 animate-pulse rounded-sm"></span>
      )}
    </span>
  )
}

// 自定义消息组件 - 与ChatDetail历史消息样式保持一致
const CaseMessage: React.FC<{ message: ChatMessage }> = ({ message }) => {
  if (message.type === 'user') {
    return (
      <div className="mx-auto max-w-4xl relative group mb-6">
        <div className="ml-auto max-w-2xl bg-gray-0 border border-gray-200 rounded-2xl rounded-br-md p-4 shadow-sm hover:shadow-md transition-all duration-200">
          <div className="text-gray-800 text-base leading-relaxed">
            <AiResponseRenderer
              content={message.content}
              fontSize="base"
            />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="mx-auto max-w-4xl relative group mb-6">
      <div className="mr-auto max-w-4xl">
        <div className="text-base text-gray-800 leading-relaxed prose prose-base max-w-none">
          {message.isGenerating ? (
            <TypewriterText text={message.content} speed={20} />
          ) : (
            <AiResponseRenderer
              content={message.content}
              fontSize="lg"
            />
          )}
        </div>
      </div>
    </div>
  )
}

// 自定义消息列表组件
const CaseMessageList: React.FC<{ messages: ChatMessage[] }> = ({ messages }) => {
  const messagesEndRef = React.useRef<HTMLDivElement>(null)

  // 自动滚动到底部
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  return (
    <div className="flex-1 px-4 py-6 pb-48 overflow-y-auto bg-gray-50">
      <div className="max-w-4xl mx-auto">
        {messages.map((message) => (
          <CaseMessage key={message.id} message={message} />
        ))}
        <div ref={messagesEndRef} />
      </div>
    </div>
  )
}

const CaseExample: React.FC<CaseExampleProps> = ({ xAiApi }) => {
  const { lang, caseId } = useParams()
  const navigate = useNavigate()
  const { t } = useSimpleTranslation()
  const [messages, setMessages] = useState<ChatMessage[]>([])

  const [isProcessing, setIsProcessing] = useState(false) // 防止重复处理
  const [hasInitialized, setHasInitialized] = useState(false) // 防止重复初始化
  const [caseTitle, setCaseTitle] = useState<string>(t('case.title')) // 存储案例标题

  // 用于存储定时器引用，便于清理
  const timersRef = React.useRef<ReturnType<typeof setTimeout>[]>([])
  // 用于防止重复API请求
  const isRequestingRef = React.useRef(false)

  // 案例不存在时的处理函数
  const handleCaseNotFound = () => {
    message.error(t('case.notFound'))
    setTimeout(() => {
      navigate(lang ? `/${lang}` : '/')
    }, 3000) // 延迟3秒后跳转
  }

  // 清理定时器的函数
  const clearAllTimers = () => {
    timersRef.current.forEach(timer => clearTimeout(timer))
    timersRef.current = []
  }

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      clearAllTimers()
      isRequestingRef.current = false
    }
  }, [])

  // 当caseId改变时重置状态
  useEffect(() => {
    return () => {
      // caseId改变时的清理工作
      clearAllTimers()
      isRequestingRef.current = false
      setIsProcessing(false)
      setHasInitialized(false)
    }
  }, [caseId])

  // 获取案例数据
  useEffect(() => {
    if (!caseId) {
      handleCaseNotFound()
      return
    }

    // 防止重复请求
    if (isRequestingRef.current) {
      return
    }

    // 如果已经初始化过相同的caseId，跳过
    if (hasInitialized && messages.length > 0) {
      return
    }

    // 清理之前的状态
    clearAllTimers()
    setIsProcessing(false)
    setMessages([])
    setHasInitialized(false)

    // 调用API获取案例数据
    const fetchCaseData = async () => {
      if (isRequestingRef.current) return

      isRequestingRef.current = true
      try {
        // 调用qaList API获取案例数据
        const response = await xAiApi.qaList({articleId:"",encryptionId: caseId})

        if (response && Array.isArray(response) && response.length > 0) {
          setHasInitialized(true)

          // 提取question字段作为标题
          const firstItem = response[0]
          if (firstItem && firstItem.question) {
            setCaseTitle(firstItem.question)
          }

          generateMessagesFromCaseData(response)
        } else {
          handleCaseNotFound()
        }
      } catch (error) {
        handleCaseNotFound()
      } finally {
        isRequestingRef.current = false
      }
    }

    fetchCaseData()

  }, [caseId]) // 只依赖caseId，移除xAiApi依赖

  // 根据案例数据生成对话消息
  const generateMessagesFromCaseData = (caseData: any) => {
    // 防止重复处理
    if (isProcessing) {
      return
    }

    // 清理之前的状态，确保干净的开始
    clearAllTimers()
    setMessages([])
    setIsProcessing(true)

    let qaItems: QAItem[] = []

    try {
      // 解析API返回的数据
      if (caseData && Array.isArray(caseData) && caseData.length > 0) {
        const firstItem = caseData[0]
        if (firstItem && firstItem.answer) {
          let answerData = firstItem.answer

          // 如果 answer 是字符串，尝试解析为 JSON
          if (typeof answerData === 'string') {
            try {
              answerData = JSON.parse(answerData)
            } catch (parseError) {
              handleCaseNotFound()
              return
            }
          }

          // 如果解析成功且是数组，使用解析的数据
          if (Array.isArray(answerData) && answerData.length > 0) {
            qaItems = answerData
          } else {
            handleCaseNotFound()
            return
          }
        } else {
          handleCaseNotFound()
          return
        }
      } else {
        handleCaseNotFound()
        return
      }

      // 验证问答数据的有效性
      if (qaItems.length === 0) {
        handleCaseNotFound()
        return
      }

    } catch (error) {
      handleCaseNotFound()
      return
    }

    // 生成对话消息，按条输出
    generateQAMessages(qaItems)
  }

  // 生成问答消息，带打字机效果
  const generateQAMessages = (qaItems: QAItem[]) => {
    // 设置处理状态
    setIsProcessing(true)

    const allMessages: ChatMessage[] = []

    // 为每个问答对生成消息
    qaItems.forEach((item, index) => {
      // 用户问题
      if (item.query && item.query.trim()) {
        const userMessage: ChatMessage = {
          id: `user_${index}_${Date.now()}`,
          type: 'user',
          content: item.query.trim(),
          timestamp: new Date()
        }
        allMessages.push(userMessage)
      }

      // AI 回答
      if (item.answer && item.answer.trim()) {
        const assistantMessage: ChatMessage = {
          id: `assistant_${index}_${Date.now()}`,
          type: 'assistant',
          content: item.answer.trim(),
          timestamp: new Date(),
          isGenerating: true // 初始状态为生成中，用于打字机效果
        }
        allMessages.push(assistantMessage)
      }
    })
    // 逐条显示消息
    showMessagesSequentially(allMessages)
  }

  // 逐条显示消息的函数
  const showMessagesSequentially = (allMessages: ChatMessage[]) => {
    if (allMessages.length === 0) {
      setIsProcessing(false)
      return
    }

    let currentIndex = 0
    setMessages([]) // 清空现有消息

    const showNextMessage = () => {
      if (currentIndex >= allMessages.length) {
        // 所有消息显示完成
        setIsProcessing(false)
        return
      }

      const currentMessage = allMessages[currentIndex]
      // 添加当前消息到列表
      setMessages(prev => [...prev, currentMessage])

      // 如果是助手消息，需要模拟打字机效果
      if (currentMessage.type === 'assistant') {
        // 计算打字机效果的时间（基于内容长度）
        const typingTime = Math.min(currentMessage.content.length * 30, 3000) // 最多3秒

        // 打字机效果完成后，移除生成状态
        const timer1 = setTimeout(() => {
          setMessages(prev =>
            prev.map(msg =>
              msg.id === currentMessage.id
                ? { ...msg, isGenerating: false }
                : msg
            )
          )

          // 延迟后显示下一条消息
          const timer2 = setTimeout(() => {
            currentIndex++
            showNextMessage()
          }, 800) // 每条消息之间间隔800ms
          timersRef.current.push(timer2)
        }, typingTime)
        timersRef.current.push(timer1)
      } else {
        // 用户消息立即显示下一条
        const timer = setTimeout(() => {
          currentIndex++
          showNextMessage()
        }, 1000) // 用户消息后稍作停顿
        timersRef.current.push(timer)
      }
    }

    // 开始显示第一条消息
    showNextMessage()
  }



  return (
    <div className="relative h-screen" style={{ backgroundColor: 'var(--bg-main)' }}>
      <div className="h-screen flex flex-col">
        {/* 案例标题 */}
        <div className="bg-white border-b border-gray-200 px-6 py-4 shadow-sm">
          <div className="max-w-4xl mx-auto">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center text-white font-medium">
                AI
              </div>
              <div>
                <h1 className="text-lg font-semibold text-gray-800">
                  {caseTitle}
                </h1>
                <p className="text-sm text-gray-600">
                  {t('case.caseId')} {caseId}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 消息列表区域 */}
        <CaseMessageList messages={messages} />
      </div>

      {/* 悬浮按钮 - 固定在屏幕左侧，不显示新建会话按钮 */}
      <FloatingButtons showNewChat={false} />
    </div>
  )
}

export default CaseExample
