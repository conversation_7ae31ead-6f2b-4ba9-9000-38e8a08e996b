import { useState, useCallback, useRef, forwardRef, useImperativeHandle } from 'react'
import { useSimpleTranslation } from '../../i18n/simple-hooks'
import './FileUpload.css'

interface FileUploadProps {
  onFilesUploaded: (files: File[]) => void | Promise<void>
  children: React.ReactNode
  allowedFileTypes?: string[]
  disabled?: boolean
  isLoading?: boolean
  maxFiles?: number
}

// 导出一个触发文件选择的函数类型
export interface FileUploadRef {
  triggerFileSelect: () => void
}

const FileUpload = forwardRef<FileUploadRef, FileUploadProps>(({ onFilesUploaded, children, allowedFileTypes = [], disabled = false, isLoading = false, maxFiles }, ref) => {
  const { t } = useSimpleTranslation()
  const [isDragOver, setIsDragOver] = useState(false)
  const [dragCounter, setDragCounter] = useState(0)
  const dragRef = useRef<HTMLDivElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    triggerFileSelect: () => {
      if (!disabled && fileInputRef.current) {
        fileInputRef.current.click()
      }
    }
  }), [disabled])

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    console.log('=====', '用户进入了拖拽区域')
    e.preventDefault()
    e.stopPropagation()

    if (disabled) return

    setDragCounter(prev => prev + 1)

    // 检查是否包含文件
    if (e.dataTransfer.types && e.dataTransfer.types.includes('Files')) {
      setIsDragOver(true)
    }
  }, [disabled])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    console.log('=====', '用户在拖拽区域')
    e.preventDefault()
    e.stopPropagation()

    if (disabled) return

    // 设置拖拽效果
    e.dataTransfer.dropEffect = 'copy'
  }, [disabled])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    console.log('=====', '用户离开了拖拽区域')
    e.preventDefault()
    e.stopPropagation()

    if (disabled) return

    setDragCounter(prev => {
      const newCounter = prev - 1
      if (newCounter === 0) {
        setIsDragOver(false)
      }
      return newCounter
    })
  }, [disabled])

  const validateFiles = useCallback((files: File[]) => {
    if (allowedFileTypes.length === 0) return files

    return files.filter(file => {
      const fileExtension = file.name.split('.').pop()?.toLowerCase()
      return fileExtension && allowedFileTypes.includes(fileExtension)
    })
  }, [allowedFileTypes])

  const handleDrop = useCallback((e: React.DragEvent) => {
    console.log('=====', '用户松开了鼠标', disabled)
    e.preventDefault()
    e.stopPropagation()

    if (disabled) return

    setIsDragOver(false)
    setDragCounter(0)

    const files = Array.from(e.dataTransfer.files)
    console.log('files', files)
    if (files.length > 0) {
      const validFiles = validateFiles(files)
      if (validFiles.length > 0) {
        onFilesUploaded(validFiles)
      }
    }
  }, [onFilesUploaded, validateFiles, disabled])

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    console.log('=====', '用户选择了文件')
    if (disabled) return

    const files = Array.from(e.target.files || [])
    if (files.length > 0) {
      const validFiles = validateFiles(files)
      if (validFiles.length > 0) {
        onFilesUploaded(validFiles)
      }
    }
    // 清空input值，允许重复选择同一文件
    e.target.value = ''
  }, [onFilesUploaded, validateFiles, disabled])

  const acceptString = allowedFileTypes.length > 0
    ? allowedFileTypes.map(type => `.${type}`).join(',')
    : '.pdf,.doc,.docx,.txt,.md,.xlsx,.xls,.pptx,.ppt,.csv'

  return (
    <div
      ref={dragRef}
      className={`relative transition-all duration-300 ease-in-out ${
        isDragOver
          ? 'ring-2 ring-blue-500 ring-opacity-50 bg-blue-50 bg-opacity-20'
          : ''
      } ${disabled ? 'pointer-events-none opacity-50' : ''}`}
      onDragEnter={handleDragEnter}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {children}

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept={acceptString}
        onChange={handleFileSelect}
        className="hidden"
        id="file-upload"
        disabled={disabled}
      />

      {/* 拖拽覆盖层 */}
      {isDragOver && !disabled && (
        <div className="absolute inset-0 drag-overlay border-2 border-dashed border-blue-400 rounded-2xl flex items-center justify-center z-50 fade-in">
          <div className="text-center p-6">
            <div className="text-4xl mb-3 file-icon-bounce">📁</div>
            <div className="text-blue-600 font-semibold text-lg mb-2">{t('fileUpload.dropToUpload')}</div>
            <div className="text-sm text-blue-500 space-y-1">
              {maxFiles && (
                <div className="font-medium">
                  最多上传 {maxFiles} 个文件
                </div>
              )}
              <div>
                {allowedFileTypes.length > 0
                  ? `支持格式: ${allowedFileTypes.join(', ')}`
                  : '支持多种格式文档和图片'
                }
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 上传加载状态覆盖层 */}
      {isLoading && (
        <div className="absolute inset-0 bg-white bg-opacity-90 rounded-2xl flex items-center justify-center z-40 fade-in">
          <div className="text-center p-6">
            <div className="text-3xl mb-3 animate-spin">⏳</div>
            <div className="text-gray-600 font-medium text-lg mb-2">正在上传文件...</div>
            <div className="text-sm text-gray-500">请稍候，文件正在处理中</div>
          </div>
        </div>
      )}
    </div>
  )
})

FileUpload.displayName = 'FileUpload'

export default FileUpload
