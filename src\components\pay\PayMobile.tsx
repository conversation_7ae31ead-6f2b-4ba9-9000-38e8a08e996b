import React, { useState, useEffect, useRef } from 'react'
import { XAiApi, IGetAiAppInfoResponse, PackageByKey, FeeType } from '../../api/src/xai-api'
import Cookies from 'js-cookie'
import { message, Modal } from 'antd'
import { formatPeriodText } from '../../api/src/utils'
import { useSimpleTranslation } from '../../i18n/simple-hooks'

// 支付方式选择组件
interface PaymentInterfaceProps {
  selectedFee: FeeType | undefined
  subscribe: (feeType: FeeType) => Promise<void>
  paymentLoading: boolean
}

const PaymentInterface: React.FC<PaymentInterfaceProps> = ({
  selectedFee,
  subscribe,
  paymentLoading
}) => {
  const { t } = useSimpleTranslation()
  const [isAgreementChecked, setIsAgreementChecked] = useState(false)
  const [selectedPeriodText, setSelectedPeriodText] = useState<string>(t('subscription.perMonth'))
  useEffect(() => {
    if (selectedFee) {
      const periodText = formatPeriodText(selectedFee.monthNum, selectedFee.periodType, t);
      setSelectedPeriodText(periodText);
      if (selectedFee.type === '免费' && !paymentLoading) {
        subscribe(selectedFee)
      }
    }
    setIsAgreementChecked(false)
  }, [selectedFee, t])

  // 切换协议选择状态
  const toggleAgreement = () => {
    setIsAgreementChecked(!isAgreementChecked);
  };

    // 跳转到协议
  const toAgreement = () => {
    window.open("https://www.medsci.cn/about/index.do?id=27")
  };



  return (
    <div className={`relative space-y-4 pb-32 ${selectedFee?.type == '免费' ? 'mt-6' : ''}`}>
      {/* 支付方式选择 */}
      {selectedFee?.type !== '免费' && (
        <div className="border border-gray-200 rounded-xl p-4 bg-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center">
                <img
                  src="https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png"
                  alt="支付宝"
                  className="w-5 h-5"
                />
              </div>
              <span className="text-gray-800 font-medium">{t('subscription.alipay')}</span>
            </div>
            <div className="w-6 h-6 rounded-full border-2 border-orange-500 bg-orange-500 flex items-center justify-center">
              <div className="w-2.5 h-2.5 bg-white rounded-full"></div>
            </div>
          </div>
        </div>
      )}

      {/* 底部操作区域 */}
      <div className="absolute bottom-6 left-0 right-0 px-4 space-y-4">
        {/* 协议选择 */}
        <div className="flex items-center gap-3 px-1">
          <button
            onClick={toggleAgreement}
            className={`w-5 h-5 min-h-0 rounded-full border-2 flex items-center justify-center transition-all duration-200 ${
              isAgreementChecked
                ? 'border-blue-500 bg-blue-500'
                : 'border-gray-300 bg-white hover:border-gray-400'
            }`}
            style={{ minHeight: 'auto' }}
          >
            {isAgreementChecked && (
              <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            )}
          </button>
          <span
            className="text-blue-600 text-sm cursor-pointer hover:text-blue-700 transition-colors"
            onClick={toAgreement}
          >
            {t('subscription.readAndAgree')}
          </span>
        </div>

        {/* 支付按钮 */}
        <button
          onClick={() => {
            if (isAgreementChecked && selectedFee) {
              subscribe(selectedFee);
            } else if (!isAgreementChecked) {
              message.warning(t('subscription.agreeToTerms'));
            }
          }}
          disabled={!isAgreementChecked}
          className={`w-full py-3 rounded-xl font-semibold text-base transition-all duration-200 ${
            isAgreementChecked
              ? 'bg-gradient-to-r from-orange-400 to-orange-500 text-white hover:from-orange-500 hover:to-orange-600 shadow-lg hover:shadow-xl'
              : 'bg-gray-200 text-gray-400 cursor-not-allowed'
          }`}
        >
          {selectedFee?.type === '免费' ? t('subscription.freeSubscription') : `¥${selectedFee?.feePrice}元${selectedPeriodText} ${t('subscription.confirmPayment')}`}
        </button>
      </div>
    </div>
  )
}

interface H5SubscriptionModalProps {
  isOpen: boolean
  onClose: () => void
  xAiApi: XAiApi
  currentApp: IGetAiAppInfoResponse
  subStatusDetail: PackageByKey
  subscribe: (feeType: FeeType) => Promise<void>
  paymentLoading: boolean
}

const PayMobile: React.FC<H5SubscriptionModalProps> = ({
  isOpen,
  onClose,
  xAiApi,
  currentApp,
  subStatusDetail,
  subscribe,
  paymentLoading
}) => {
  const { t } = useSimpleTranslation()
  const defAvatar = 'https://img.medsci.cn/web/img/user_icon.png'
  const [selectedFee, setSelectedFee] = useState<FeeType>()
  const [userInfoString] = useState(Cookies.get('userInfo'))
  const [userInfo] = useState(() => {
    return userInfoString ? JSON.parse(userInfoString) : null
  })
  const [avatar, setAvatar] = useState(userInfo?.avatar || defAvatar)
  const [realName] = useState(userInfo?.realName || userInfo?.username || '')
  const [isVisible, setIsVisible] = useState(false)

  // 当应用切换时重置选择状态
  useEffect(() => {
    setSelectedFee(undefined)
  }, [currentApp?.appUuid])

  // 当弹框打开时初始化选择和动画
  useEffect(() => {
    if (isOpen && subStatusDetail) {
      initializeSelection()
      // 延迟一帧来触发动画
      requestAnimationFrame(() => {
        setIsVisible(true)
      })
    } else {
      setIsVisible(false)
    }
  }, [isOpen, subStatusDetail])

  // ESC键关闭弹窗功能
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        close()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown)
      return () => {
        document.removeEventListener('keydown', handleKeyDown)
      }
    }
  }, [isOpen])

  // 初始化选择第一个套餐
  const initializeSelection = () => {
    const fee = subStatusDetail?.feeTypes.find(item => item.type === subStatusDetail.packageType)
    handlePlanSelect(fee ? fee : subStatusDetail.feeTypes[0])
  }

  const handlePlanSelect = (feeType: FeeType) => {
    setSelectedFee(feeType)
  }

  // 获取套餐类型的国际化名称
  const getPlanTypeName = (type: string): string => {
    const typeLower = type.toLowerCase()
    if (typeLower === '免费' || typeLower === 'free') {
      return t('subscription.planType.free')
    } else if (typeLower.includes('周') || typeLower.includes('week')) {
      return t('subscription.planType.weekly')
    } else if (typeLower.includes('月') || typeLower.includes('month')) {
      return t('subscription.planType.monthly')
    }
    return type // 如果没有匹配的翻译，返回原始类型
  }

  if (!isOpen) return null

  const errorImg = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    console.log('=====', '用户头像加载失败，使用默认头像')
    const target = e.target as HTMLImageElement
    target.src = defAvatar
    setAvatar(defAvatar)
  }

  // 关闭弹框
  const close = () => {
    setIsVisible(false)
    // 等待动画完成后再调用onClose
    setTimeout(() => {
      onClose()
    }, 300)
  }

  // 取消订阅
  const cancelSub = () => {
    Modal.confirm({
      title: t('subscription.confirmTitle'),
      zIndex: 4000,
      okButtonProps: { style: { backgroundColor: '#D7813F', borderColor: '#D7813F' } },
      content: t('subscription.confirmCancel').replace('{expireTime}', subStatusDetail?.expireAt || ''),
      onOk: async () => {
        const res = await xAiApi.cancelSubscription();
        message.success(t('subscription.cancelSuccess'));
        close()
      },
      onCancel: () => {
        // on cancel
      }
    });
  };

  return (
    <div className="fixed inset-0 z-50 flex items-end justify-center">
      {/* 背景遮罩 */}
      <div
        className="absolute inset-0 bg-black bg-opacity-30"
        onClick={close}
      />

      {/* H5弹框内容 - 从底部滑出 */}
      <div
        className={`h5-subscription-modal relative bg-white rounded-t-3xl shadow-2xl w-full max-h-[85vh] overflow-y-auto transform transition-transform duration-300 ease-out ${
          isVisible ? 'translate-y-0' : 'translate-y-full'
        }`}
        style={{
          background: 'linear-gradient(to bottom, #fef9f3, #fefcf8, #fefefe, #ffffff)'
        }}
      >

        {/* 关闭按钮行 */}
        <div className="flex justify-end px-6 pt-3 pb-2 bg-white">
          <button
            onClick={close}
            className="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
            style={{ minHeight: 'auto' }}
          >
            <span className="text-gray-500 text-lg">×</span>
          </button>
        </div>

        {/* 头像和应用信息行 */}
        <div className="px-6 p-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-3">
              {userInfo && (
                <div className="w-10 h-10 bg-gradient-to-br rounded-xl flex items-center justify-center">
                  {userInfo.avatar ? (
                    <img
                      src={avatar} alt={realName} onError={errorImg}
                      className="w-full h-full object-cover rounded-xl"
                    />
                  ) : null}
                </div>
              )}
              <div className="text-left">
                <div className="text-gray-900">{realName}</div>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <div className="w-12 h-12 flex items-center justify-center">
                <img src={currentApp.appIcon} alt={currentApp.appName} className="w-12 h-12" />
              </div>
              <div className="text-right">
                <h2 className="text-lg font-bold text-gray-900">{currentApp.appName}</h2>
              </div>
            </div>
          </div>
        </div>

        <div className="text-sm text-gray-700 leading-relaxed mb-6 whitespace-pre-line text-center">
            {t('subscription.desc.'+currentApp.appNameEn)}
        </div>

        {/* 套餐选择区域 */}
        <div className="px-4 overflow-y-auto">
          {/* 套餐网格布局 - 一行3个 */}
          <div className="grid grid-cols-3 gap-2 mb-6 max-h-48 ">
            {subStatusDetail?.feeTypes.map((fee) => {
              // 判断是否禁用：当前已订阅非免费套餐时，禁止点击其他套餐
              const isDisabled = (subStatusDetail?.subStatus === 1 && subStatusDetail?.packageType !== '免费') || subStatusDetail?.subStatus === 3;

              return (
                <div
                  key={fee.type}
                  className={`relative rounded-xl border-2 p-3 transition-all duration-200 ${isDisabled && selectedFee?.type !== fee.type
                      ? 'cursor-not-allowed opacity-50 bg-gray-100 border-gray-200'
                      : `cursor-pointer hover:shadow-lg ${selectedFee?.type === fee.type
                        ? 'border-blue-400 bg-blue-50 shadow-md'
                        : 'border-gray-200 bg-white hover:border-gray-300'
                      }`
                    }`}
                  onClick={() => !isDisabled && handlePlanSelect(fee)}
                >
                  {/* 套餐名称 */}
                  <div className="text-center">
                    <h4 className="text-sm font-semibold text-gray-900 mb-2">{getPlanTypeName(fee.type)}</h4>
                    <div className="flex flex-col items-center">
                      <span className="text-lg font-bold text-gray-900">¥{fee.feePrice}</span>
                      {fee.oldPrice && fee.oldPrice !== fee.feePrice && (
                        <span className="text-xs text-gray-400 line-through">{t('subscription.standardPrice')}：¥{fee.oldPrice}</span>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* 订阅状态显示 */}
        <div className="mx-4 bg-white border border-gray-200 rounded-lg p-4 mb-4 min-h-[200px]">
            {subStatusDetail?.subStatus === 1 && (
              // 订阅中
              (subStatusDetail.packageType === '免费' && selectedFee?.type !== '免费') ? (
                <PaymentInterface
                  selectedFee={selectedFee}
                  subscribe={subscribe}
                  paymentLoading={paymentLoading}
                />
                
              ) : (
                <div className="text-center">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    {/* 图标 */}
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <span className="text-green-600 text-sm">✓</span>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900">{t('subscription.subscribing')}</h3>
                  </div>
                  <p className="text-gray-600 mb-2">{t('subscription.subscriptionActive')}</p>
                  <p className="text-sm text-gray-500 mb-4">{subStatusDetail?.subAt} {t('subscription.subscribed')}</p>
                  {subStatusDetail.packageType !== '免费' && (
                    <button
                      onClick={cancelSub}
                      className="px-4 py-2 text-sm text-red-600 border border-red-200 rounded-lg hover:bg-red-50 transition-colors"
                    >
                      {t('subscription.cancelSubscription')}
                    </button>
                  )}
                </div>
              )
            )}

            {subStatusDetail?.subStatus === 3 && (
              // 退订中状态
              <div>
                <div className="text-center">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    {/* 图标 */}
                    <div className="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <span className="text-orange-600 text-sm">⏳</span>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900">{t('subscription.cancelled')}</h3>
                  </div>
                  <p className="text-gray-600 mb-2">{t('subscription.subscriptionCancelled')}</p>
                  <p className="text-sm text-gray-500">{t('subscription.expireTime')}：{subStatusDetail?.expireAt}</p>
                </div>
              </div>
            )}

            {(!subStatusDetail?.subStatus || subStatusDetail?.subStatus === 0 || subStatusDetail?.subStatus === 2) && (
              // 未订阅 或 已过期状态
              <PaymentInterface
                  selectedFee={selectedFee}
                  subscribe={subscribe}
                  paymentLoading={paymentLoading}
                />
            )}
        </div>
      </div>
      
    </div>
  )
}

export default PayMobile
