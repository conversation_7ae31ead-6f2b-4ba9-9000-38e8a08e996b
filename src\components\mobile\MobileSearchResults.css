/* 移动端搜索结果组件样式 */
.mobile-search-results {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 16px;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 主容器头部 */
.search-results-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  cursor: pointer;
  user-select: none;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.search-results-header:hover {
  background-color: #f8f9fa;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.search-icon {
  width: 18px;
  height: 18px;
  color: #666;
  flex-shrink: 0;
}

.search-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.source-count {
  font-size: 14px;
  color: #666;
  font-weight: normal;
}

.chevron-icon {
  width: 20px;
  height: 20px;
  color: #666;
  transition: transform 0.3s ease;
  flex-shrink: 0;
}

.chevron-icon.expanded {
  transform: rotate(180deg);
}

/* 展开状态提示 */
.expand-status {
  padding: 8px 20px;
  font-size: 12px;
  color: #999;
  background-color: #fafafa;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

/* 结果容器 */
.results-container {
  overflow: hidden;
  transition: max-height 0.4s ease-in-out, opacity 0.3s ease-in-out;
}

.results-container.collapsed {
  max-height: 0;
  opacity: 0;
}

.results-container.expanded {
  max-height: 2000px;
  opacity: 1;
}

.results-content {
  padding: 0;
}

/* 单个结果项 */
.result-item {
  border-bottom: 1px solid #f0f0f0;
}

.result-item:last-child {
  border-bottom: none;
}

.result-item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease;
}

.result-item-header:hover {
  background-color: #f8f9fa;
}

.result-header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

/* 状态圆点 */
.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-dot-red {
  background-color: #ff4757;
}

.status-dot-blue {
  background-color: #3742fa;
}

.status-dot-green {
  background-color: #2ed573;
}

.status-dot-orange {
  background-color: #ffa502;
}

.status-dot-purple {
  background-color: #a55eea;
}

.result-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
}

.result-chevron {
  width: 16px;
  height: 16px;
  color: #999;
  transition: transform 0.3s ease;
  flex-shrink: 0;
}

.result-chevron.expanded {
  transform: rotate(180deg);
}

/* 结果项内容 */
.result-item-content {
  overflow: hidden;
  transition: max-height 0.4s ease-in-out, opacity 0.3s ease-in-out;
}

.result-item-content.collapsed {
  max-height: 0;
  opacity: 0;
}

.result-item-content.expanded {
  max-height: 1000px;
  opacity: 1;
}

.result-content-inner {
  padding: 0 20px 16px 20px;
}

/* 结果描述 */
.result-description {
  font-size: 14px;
  line-height: 1.6;
  color: #555;
  margin-bottom: 12px;
}

/* 详细信息列表 */
.result-details {
  margin-bottom: 16px;
}

.details-list {
  margin: 0;
  padding-left: 20px;
  list-style: none;
}

.detail-item {
  position: relative;
  font-size: 14px;
  line-height: 1.6;
  color: #555;
  margin-bottom: 8px;
  padding-left: 0;
}

.detail-item::before {
  content: '•';
  color: #999;
  position: absolute;
  left: -16px;
  top: 0;
}

/* 来源链接 */
.result-sources {
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

.sources-title {
  font-size: 13px;
  font-weight: 600;
  color: #666;
  margin-bottom: 8px;
}

.source-item {
  margin-bottom: 8px;
}

.source-link {
  display: block;
  text-decoration: none;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

.source-link:hover {
  background-color: #e9ecef;
  border-color: #dee2e6;
  transform: translateY(-1px);
}

.source-title {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  margin-bottom: 2px;
}

.source-domain {
  font-size: 12px;
  color: #666;
  line-height: 1.3;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .mobile-search-results {
    margin: 12px;
    border-radius: 10px;
  }
  
  .search-results-header,
  .result-item-header {
    padding: 14px 16px;
  }
  
  .result-content-inner {
    padding: 0 16px 14px 16px;
  }
  
  .search-title {
    font-size: 15px;
  }
  
  .source-count {
    font-size: 13px;
  }
  
  .result-title {
    font-size: 14px;
  }
  
  .result-description,
  .detail-item {
    font-size: 13px;
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  .mobile-search-results {
    margin: 8px;
    border-radius: 8px;
  }
  
  .search-results-header,
  .result-item-header {
    padding: 12px 14px;
  }
  
  .result-content-inner {
    padding: 0 14px 12px 14px;
  }
  
  .header-left {
    gap: 6px;
  }
  
  .result-header-left {
    gap: 10px;
  }
}
