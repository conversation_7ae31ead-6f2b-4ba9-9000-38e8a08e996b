import React, { useState } from 'react';
import MobileSearchResults from './MobileSearchResults';

const MobileSearchResultsExample: React.FC = () => {
  const [isExpanded, setIsExpanded] = useState(false);

  // 示例数据
  const searchResults = [
    {
      id: '1',
      title: '翻译和理解问题',
      content: '您好！为了确保我们的工作顺利开展，请上传最新的用户输入文件和先前的所有分析资料，这样我可以更详细地了解具体情况并与之对比。同时，在此过程中我会再次确认关键信息如下：',
      details: [
        '1. 您的研究领域(Research_Field): 请注意检查是否有遗漏的关键字或术语常见的具体的学术领域。学科分支或研究方向。',
        '2. 您的研究基础(Research_Foundation): 请审查是否存在关于已发表的文章列表或其他材料成果的相关说明文字，特别是那些基础研究成果的地方。',
        '3. 您的研究条件(Research_Condition)'
      ]
    },
    {
      id: '2', 
      title: '总结价格动态',
      content: '根据最新的市场分析，美国鸡蛋价格在2025年预计将出现显著变化。',
      details: [
        '截至2025年7月3日，美国鸡蛋价格为每打2.56美元，比前一天高，但低于今年早些时候的峰值。过去一个月价格上涨0.79%，同比去年同期涨1.22%。',
        '预测显示，除非产蛋母鸡数量显著复苏，涨价趋势可能延续至2025年下半年。高流感疫情和供应链问题是主要原因。'
      ],
      sources: [
        {
          title: '美国鸡蛋短缺 消费者怒斥零售商 专售高价施2025',
          url: 'https://sohu.com/example',
          domain: 'sohu.com'
        },
        {
          title: '美国鸡蛋价格飙升：高流感、消费者怒斥与缺货...',
          url: 'https://forwardpathway.com/example', 
          domain: 'forwardpathway.com'
        },
        {
          title: '价格飙涨近新供应蛋供应缺货将成美国2025年...',
          url: 'https://wenxuecity.com/example',
          domain: 'wenxuecity.com'
        }
      ]
    },
    {
      id: '3',
      title: '评估消费者反应',
      content: '消费者对价格上涨的反应呈现多样化趋势，主要体现在购买行为和消费习惯的改变。'
    },
    {
      id: '4', 
      title: '整合市场影响',
      content: '综合分析显示，鸡蛋价格波动对整个食品市场产生了连锁反应。'
    },
    {
      id: '5',
      title: '探索更多见解', 
      content: '深入研究表明，这一现象背后存在更复杂的经济和社会因素。'
    },
    {
      id: '6',
      title: '整合消费者影响',
      content: '消费者行为模式的变化反映了市场适应性和弹性。'
    },
    {
      id: '7',
      title: '研究政策影响',
      content: '政府政策和监管措施在价格稳定中发挥着重要作用。'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-md mx-auto">
        <h1 className="text-2xl font-bold text-gray-900 mb-6 text-center">
          移动端搜索结果组件
        </h1>
        
        <MobileSearchResults
          title="DeepSearch"
          totalSources={34}
          results={searchResults}
          isExpanded={isExpanded}
          onToggle={() => setIsExpanded(!isExpanded)}
        />

        {/* 使用说明 */}
        <div className="mt-8 bg-white rounded-lg p-4 shadow-sm">
          <h2 className="text-lg font-semibold text-gray-900 mb-3">功能特性</h2>
          <ul className="space-y-2 text-sm text-gray-600">
            <li>• 整体折叠/展开动画效果</li>
            <li>• 单个项目独立折叠控制</li>
            <li>• 平滑的CSS过渡动画</li>
            <li>• 移动端优化的触摸体验</li>
            <li>• 响应式设计适配</li>
            <li>• 支持多种内容类型展示</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default MobileSearchResultsExample;
