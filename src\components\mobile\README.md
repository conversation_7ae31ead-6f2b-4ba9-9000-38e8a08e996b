# 移动端搜索结果组件 (MobileSearchResults)

## 概述

`MobileSearchResults` 是一个专为移动端设计的搜索结果展示组件，用于替换桌面端的双面板布局。组件采用"左右节点插入/集成"的设计理念，提供统一的移动端用户体验。

## 主要特性

### 🎨 视觉设计
- **圆角卡片式设计**：白色背景，带阴影效果
- **DeepSearch风格**：仿照DeepSearch搜索结果的UI设计
- **彩色状态圆点**：支持红、蓝、绿、橙、紫色状态指示
- **平滑动画**：300-400ms的ease-in-out过渡效果

### 📱 移动端适配
- **响应式设计**：适配320px-768px屏幕宽度
- **触摸友好**：最小44px的点击区域
- **字体优化**：不同屏幕尺寸的字体大小适配
- **横竖屏支持**：自动适应屏幕方向变化

### 🔧 功能特性
- **双级折叠**：整体容器和单个结果项的独立折叠
- **数据源合并**：自动处理多种数据源的合并显示
- **状态管理**：独立的展开/折叠状态管理
- **回调支持**：提供节点点击和容器切换回调

## 使用方法

### 基础用法

```tsx
import { MobileSearchResults } from '@/components/mobile';

// 基础搜索结果显示
<MobileSearchResults
  title="DeepSearch"
  totalSources={34}
  results={searchResults}
  isExpanded={isExpanded}
  onToggle={handleToggle}
/>
```

### 工作流节点集成

```tsx
// 替换AssistantMessage中的双面板布局
<MobileSearchResults
  title="工作流执行"
  workflowNodes={workflowNodes}
  leftPanelNodes={leftPanelNodes}
  rightPanelNodes={rightPanelNodes}
  isExpanded={isExpanded}
  onToggle={handleToggle}
  onNodeClick={handleNodeClick}
/>
```

### 混合数据源

```tsx
// 同时显示搜索结果和工作流节点
<MobileSearchResults
  title="综合搜索"
  results={searchResults}
  rightPanelNodes={rightPanelNodes}
  totalSources={totalCount}
  isExpanded={isExpanded}
  onToggle={handleToggle}
  onNodeClick={handleNodeClick}
/>
```

## Props 接口

```tsx
interface MobileSearchResultsProps {
  title?: string;                    // 组件标题，默认"DeepSearch"
  totalSources?: number;             // 来源数量，默认自动计算
  results?: SearchResultItem[];      // 搜索结果数据
  workflowNodes?: WorkflowNode[];    // 工作流节点数据
  leftPanelNodes?: WorkflowNode[];   // 左面板节点数据
  rightPanelNodes?: WorkflowNode[];  // 右面板节点数据
  isExpanded?: boolean;              // 是否展开，默认false
  onToggle?: () => void;             // 容器切换回调
  onNodeClick?: (nodeId: string) => void; // 节点点击回调
  className?: string;                // 自定义CSS类名
}
```

## 数据结构

### SearchResultItem

```tsx
interface SearchResultItem {
  id: string;                        // 唯一标识
  title: string;                     // 结果标题
  content?: string;                  // 内容描述
  details?: string[];                // 详细信息列表
  sources?: Array<{                  // 来源链接
    title: string;
    url: string;
    domain: string;
  }>;
  statusColor?: 'red' | 'blue' | 'green' | 'orange' | 'purple';
}
```

### WorkflowNode

```tsx
interface WorkflowNode {
  id: string;                        // 节点ID
  title: string;                     // 节点标题
  status: 'running' | 'success' | 'error' | 'pending'; // 执行状态
  content?: string;                  // 节点内容
  details?: string[];                // 详细信息
  sources?: Array<{                  // 来源链接
    title: string;
    url: string;
    domain: string;
  }>;
  displayCategory?: 'left' | 'right'; // 显示分类
  created_at: number;                // 创建时间
}
```

## 在AssistantMessage中的集成

### 1. 导入组件

```tsx
import { MobileSearchResults } from '@/components/mobile';
```

### 2. 添加移动端条件渲染

```tsx
// 在AssistantMessage组件的return语句中添加
{/* 移动端搜索结果组件 */}
<div className="block md:hidden">
  <MobileSearchResults
    title="搜索结果"
    workflowNodes={workflowNodes}
    leftPanelNodes={leftPanelNodes}
    rightPanelNodes={rightPanelNodes}
    isExpanded={isMobileSearchExpanded}
    onToggle={handleMobileSearchToggle}
    onNodeClick={handleNodeClick}
  />
</div>

{/* 桌面端双面板布局 */}
<div className="hidden md:block">
  {/* 现有的桌面端布局代码 */}
</div>
```

### 3. 添加状态管理

```tsx
const [isMobileSearchExpanded, setIsMobileSearchExpanded] = useState(false);

const handleMobileSearchToggle = () => {
  setIsMobileSearchExpanded(!isMobileSearchExpanded);
};
```

## 样式定制

组件使用CSS模块化设计，可以通过以下方式定制样式：

### 1. 通过className属性

```tsx
<MobileSearchResults
  className="custom-search-results"
  // ... 其他props
/>
```

### 2. 覆盖CSS变量

```css
.custom-search-results {
  --primary-color: #your-color;
  --border-radius: 8px;
  --animation-duration: 200ms;
}
```

## 性能优化

- 使用 `React.memo` 进行组件记忆化
- 使用 `useCallback` 优化事件处理函数
- 使用 `useMemo` 缓存计算结果
- 支持虚拟滚动（大数据量时）

## 浏览器兼容性

- iOS Safari 12+
- Android Chrome 70+
- 支持现代移动浏览器的所有主要功能
- 渐进式降级支持

## 注意事项

1. **数据优先级**：rightPanelNodes > leftPanelNodes > workflowNodes
2. **触摸优化**：所有交互元素都有最小44px的点击区域
3. **性能考虑**：大量数据时建议使用分页或虚拟滚动
4. **无障碍访问**：支持键盘导航和屏幕阅读器

## 示例

查看 `MobileSearchResultsExample.tsx` 文件获取完整的使用示例。
